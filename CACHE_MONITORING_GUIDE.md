# Cache Monitoring Guide - Quote Craft Profit

## 🎯 Quick Access to Cache Monitoring

### **Frontend Monitoring (React Query DevTools)**

1. **Access DevTools**: 
   - Open your browser's developer tools (F12)
   - Look for the React Query DevTools icon in the bottom-left corner
   - Click to open the cache inspector

2. **What You Can See**:
   - All active queries and their cache status
   - Query keys and cached data
   - Stale time and cache expiration
   - Background refetch status
   - Query dependencies and invalidation

### **Backend Cache Dashboard**

**URL**: `http://localhost:3000/admin/cache` (Frontend Dashboard)

**Direct API Endpoints**:
```bash
# Base URL
BASE_URL="http://localhost:5000"

# Quick status check
curl ${BASE_URL}/cache/status

# Detailed metrics
curl ${BASE_URL}/cache/metrics

# Health check
curl ${BASE_URL}/cache/health

# Performance dashboard
curl ${BASE_URL}/cache/dashboard

# Trigger manual check
curl -X POST ${BASE_URL}/cache/monitoring/check
```

## 📊 Understanding Cache Metrics

### **Key Performance Indicators (KPIs)**

#### **1. Hit Rate** 🎯
- **Good**: > 70%
- **Acceptable**: 50-70%
- **Poor**: < 50%

```json
{
  "hitRate": 87.3,  // 87.3% of requests served from cache
  "status": "healthy"
}
```

#### **2. Error Rate** ⚠️
- **Good**: < 2%
- **Acceptable**: 2-5%
- **Critical**: > 5%

#### **3. Memory Usage** 💾
- **Good**: < 80%
- **Warning**: 80-90%
- **Critical**: > 90%

#### **4. Response Times** ⚡
- **Cache Hit**: 5-50ms
- **Cache Miss**: 200-800ms
- **Database Query**: 500-2000ms

### **Cache Status Meanings**

| Status | Hit Rate | Error Rate | Action Required |
|--------|----------|------------|-----------------|
| **Healthy** | > 70% | < 2% | ✅ No action needed |
| **Degraded** | 40-70% | 2-5% | ⚠️ Monitor closely |
| **Unhealthy** | < 40% | > 5% | 🚨 Immediate attention |

## 🔍 Monitoring Scenarios

### **Scenario 1: Application Startup**

**Expected Behavior**:
```
[00:00] Cache warming starts
[00:01] Cache miss for: packages:popular
[00:01] Cache miss for: categories:all
[00:02] Cache warming completed
[00:03] Hit rate: 0% (normal during startup)
[00:05] Hit rate: 85%+ (after warming)
```

**What to Watch**:
- Cache warming should complete within 5 seconds
- Hit rate should reach 70%+ within 2 minutes
- No repeated cache misses for the same keys

### **Scenario 2: Normal Operation**

**Expected Metrics**:
```json
{
  "hitRate": 85.0,
  "errorRate": 0.1,
  "totalEntries": 150,
  "memoryUsage": {
    "percentage": 15
  }
}
```

**Red Flags**:
- Hit rate dropping below 60%
- Error rate above 2%
- Memory usage above 80%
- Repeated cache misses

### **Scenario 3: High Load**

**Expected Behavior**:
- Hit rate may temporarily drop to 60-70%
- Response times increase slightly
- Memory usage increases
- More cache operations per second

**Monitoring Actions**:
```bash
# Check current performance
curl ${BASE_URL}/cache/metrics

# Trigger performance test
curl -X POST ${BASE_URL}/cache/test/performance \
  -H "Content-Type: application/json" \
  -d '{"iterations": 1000}'
```

## 🚨 Alert Thresholds and Actions

### **Critical Alerts** 🚨

#### **Hit Rate < 30%**
```bash
# Check cache status
curl ${BASE_URL}/cache/status

# Trigger manual warming
curl -X POST ${BASE_URL}/cache/warm

# Check for errors
curl ${BASE_URL}/cache/alerts
```

#### **Error Rate > 10%**
```bash
# Check cache health
curl ${BASE_URL}/cache/health

# Reset metrics to clear old errors
curl -X POST ${BASE_URL}/cache/metrics/reset

# Check backend logs
docker logs event-costing-api
```

#### **Memory Usage > 90%**
```bash
# Clear non-essential cache
curl -X DELETE ${BASE_URL}/cache/keys \
  -H "Content-Type: application/json" \
  -d '{"pattern": "temp:*"}'

# Check cache size
curl ${BASE_URL}/cache/stats
```

### **Warning Alerts** ⚠️

#### **Hit Rate 40-60%**
- Monitor for 10 minutes
- Check if it's due to new data or cache expiration
- Consider extending TTL for frequently accessed data

#### **Error Rate 2-5%**
- Check backend connectivity
- Review error logs
- Monitor for pattern in failed keys

## 🛠️ Troubleshooting Common Issues

### **Issue 1: Low Hit Rate**

**Diagnosis**:
```bash
# Check which keys are missing
curl ${BASE_URL}/cache/metrics | jq '.topKeys'

# Check cache warming status
curl ${BASE_URL}/cache/warming/status
```

**Solutions**:
1. **Trigger manual warming**:
   ```bash
   curl -X POST ${BASE_URL}/cache/warm
   ```

2. **Increase TTL for frequently accessed data**:
   ```bash
   curl -X POST ${BASE_URL}/cache/keys/packages:popular \
     -H "Content-Type: application/json" \
     -d '{"ttl": 14400}'  # 4 hours
   ```

### **Issue 2: High Error Rate**

**Diagnosis**:
```bash
# Check error details
curl ${BASE_URL}/cache/alerts

# Check backend health
curl ${BASE_URL}/health
```

**Solutions**:
1. **Reset cache metrics**:
   ```bash
   curl -X POST ${BASE_URL}/cache/metrics/reset
   ```

2. **Clear problematic cache entries**:
   ```bash
   curl -X DELETE ${BASE_URL}/cache/all \
     -H "Content-Type: application/json" \
     -d '{"force": true}'
   ```

### **Issue 3: Memory Issues**

**Diagnosis**:
```bash
# Check memory usage
curl ${BASE_URL}/cache/stats

# Check cache size
curl ${BASE_URL}/cache/status | jq '.totalEntries'
```

**Solutions**:
1. **Clear old cache entries**:
   ```bash
   curl -X DELETE ${BASE_URL}/cache/keys \
     -H "Content-Type: application/json" \
     -d '{"pattern": "old:*"}'
   ```

2. **Reduce TTL for less critical data**:
   ```bash
   # Reduce TTL for venue data
   curl -X POST ${BASE_URL}/cache/keys/venues:popular \
     -H "Content-Type: application/json" \
     -d '{"ttl": 1800}'  # 30 minutes
   ```

## 📈 Performance Optimization Tips

### **1. Monitor Key Patterns**
```bash
# Check most accessed keys
curl ${BASE_URL}/cache/metrics | jq '.topKeys[] | {key: .key, hits: .hits}'
```

### **2. Optimize TTL Values**
- **Static data** (cities, categories): 12+ hours
- **User data** (clients): 2-4 hours
- **Dynamic data** (calculations): 30 minutes - 2 hours

### **3. Cache Warming Strategy**
```bash
# Warm high-priority caches
curl -X POST ${BASE_URL}/cache/warm \
  -H "Content-Type: application/json" \
  -d '{"keys": ["categories:all", "packages:popular", "cities:all"]}'
```

### **4. Regular Health Checks**
```bash
# Set up monitoring script
#!/bin/bash
while true; do
  HEALTH=$(curl -s ${BASE_URL}/cache/health | jq -r '.status')
  echo "$(date): Cache status: $HEALTH"
  if [ "$HEALTH" != "healthy" ]; then
    echo "ALERT: Cache is $HEALTH"
    # Send notification or trigger recovery
  fi
  sleep 300  # Check every 5 minutes
done
```

## 🎯 Success Metrics

### **Target Performance**
- **Hit Rate**: 80%+ consistently
- **Error Rate**: < 1%
- **Memory Usage**: < 50%
- **Response Time**: < 100ms for cache hits

### **Monitoring Schedule**
- **Real-time**: React Query DevTools during development
- **Every 5 minutes**: Automated health checks
- **Daily**: Performance review and optimization
- **Weekly**: Cache strategy review and tuning

## 🔗 Quick Links

- **Frontend Dashboard**: `http://localhost:3000/admin/cache`
- **Backend API**: `http://localhost:5000/cache/*`
- **React Query DevTools**: Browser developer tools
- **Cache Documentation**: `CACHE_OPTIMIZATION_IMPLEMENTATION_SUMMARY.md`

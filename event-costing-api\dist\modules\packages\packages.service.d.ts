import { SupabaseService } from '../../core/supabase/supabase.service';
import { CacheService } from '../../core/cache/cache.service';
import { ListPackageVariationsDto } from './dto/list-package-variations.dto';
import { PackageVariationDto } from './dto/package-variation.dto';
import { PackageOptionDetailDto } from './dto/package-option-detail.dto';
import { PaginatedResponseDto } from '../../shared/dtos/paginated-response.dto';
import { PackagesByCategoryResponseDto } from './dto/packages-by-category-response.dto';
import { BatchPackageOptionsResponseDto } from './dto/batch-package-options-response.dto';
export declare class PackagesService {
    private readonly supabaseService;
    private readonly cacheService;
    private readonly logger;
    constructor(supabaseService: SupabaseService, cacheService: CacheService);
    private invalidatePackageCaches;
    findVariations(queryDto: ListPackageVariationsDto): Promise<PaginatedResponseDto<PackageVariationDto>>;
    private sortPackageVariations;
    private generatePackageVariationsCacheKey;
    private fetchPackageVariations;
    findOptions(packageId: string, currencyId?: string, venueId?: string): Promise<PackageOptionDetailDto[]>;
    private fetchPackageOptions;
    private checkCityAvailability;
    private checkVenueAvailability;
    private checkVenuesAvailability;
    private checkConflicts;
    getPackagesByCategory(currencyId: string, cityId?: string, venueId?: string, includeOptions?: boolean): Promise<PackagesByCategoryResponseDto>;
    private fetchPackagesByCategory;
    private organizePackagesByCategory;
    private addOptionsToPackages;
    getBatchPackageOptions(packageIds: string[], currencyId: string, venueId?: string): Promise<BatchPackageOptionsResponseDto>;
    private fetchBatchPackageOptions;
}

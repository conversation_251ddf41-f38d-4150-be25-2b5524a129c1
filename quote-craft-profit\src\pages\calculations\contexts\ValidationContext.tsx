/**
 * Centralized UUID Validation Context for Calculation Detail Pages
 *
 * This context validates calculation IDs once and shares results across components,
 * eliminating redundant validation calls and improving performance.
 *
 * Phase 1 of UUID Validation Optimization Plan
 */

import React, { createContext, useMemo } from "react";
import { useValidationPerformance } from "../hooks/performance/usePerformanceMonitoring";
// Phase 4 Optimization: Import validation cache (replaces direct isValidUUID usage)
import { validationCache } from "@/lib/validationCache";

/**
 * Validation context type definition
 */
export interface ValidationContextType {
  /** Whether the calculation ID is valid */
  isValid: boolean;

  /** The validated calculation ID (null if invalid) */
  validId: string | null;

  /** Whether data loading should proceed */
  shouldLoadData: boolean;

  /** Error message if validation failed */
  validationError?: string;

  /** Original ID that was validated (for debugging) */
  originalId?: string;
}

/**
 * Validation context - provides validation state to child components
 */
const ValidationContext = createContext<ValidationContextType | null>(null);

/**
 * Props for ValidationProvider component
 */
interface ValidationProviderProps {
  /** The calculation ID to validate */
  id: string | undefined;

  /** Child components that will consume validation context */
  children: React.ReactNode;
}

/**
 * ValidationProvider component
 *
 * Performs single UUID validation and provides results to all child components.
 * This eliminates redundant validation calls throughout the component tree.
 *
 * @param id - The calculation ID from route parameters
 * @param children - Child components that need validation context
 */
export const ValidationProvider: React.FC<ValidationProviderProps> = ({
  id,
  children,
}) => {
  // PHASE 3 OPTIMIZATION: Add performance tracking for validation
  const { trackValidation } = useValidationPerformance("ValidationProvider");

  // Single validation point - memoized to prevent re-validation on re-renders
  const validationResult = useMemo(() => {
    // Handle undefined or empty ID
    if (!id || typeof id !== "string") {
      return {
        isValid: false,
        validId: null,
        shouldLoadData: false,
        validationError: "Calculation ID is required",
        originalId: id,
      };
    }

    // PHASE 4 OPTIMIZATION: Use validation cache with performance tracking
    const trimmedId = id.trim();
    const isValid = trackValidation(trimmedId, () =>
      validationCache.validate(trimmedId)
    );

    if (!isValid) {
      return {
        isValid: false,
        validId: null,
        shouldLoadData: false,
        validationError: "Invalid calculation ID format",
        originalId: id,
      };
    }

    // Valid UUID
    return {
      isValid: true,
      validId: trimmedId,
      shouldLoadData: true,
      validationError: undefined,
      originalId: id,
    };
  }, [id, trackValidation]);

  // Debug logging in development
  if (process.env.NODE_ENV === "development") {
    console.log("[ValidationContext] Validation result:", {
      originalId: id,
      isValid: validationResult.isValid,
      validId: validationResult.validId,
      error: validationResult.validationError,
    });
  }

  return (
    <ValidationContext.Provider value={validationResult}>
      {children}
    </ValidationContext.Provider>
  );
};

// Hooks moved to validationHooks.ts to satisfy React Fast Refresh requirements

export default ValidationContext;

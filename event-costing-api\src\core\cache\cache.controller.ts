/**
 * Cache Management Controller
 *
 * PHASE 2: Provides API endpoints for cache monitoring, management, and debugging
 * Enables real-time cache performance monitoring and manual cache operations
 */

import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
} from '@nestjs/common';
import { CacheService } from './cache.service';
import { CacheWarmingService } from './cache-warming.service';
import { CacheMonitoringService } from './cache-monitoring.service';

export class CacheOperationDto {
  keys?: string[];
  pattern?: string;
  force?: boolean;
}

export class CacheWarmingDto {
  keys?: string[];
  priority?: number;
}

@Controller('cache')
export class CacheController {
  constructor(
    private readonly cacheService: CacheService,
    private readonly cacheWarmingService: CacheWarmingService,
    private readonly cacheMonitoringService: CacheMonitoringService,
  ) {}

  /**
   * Get cache status overview
   */
  @Get('status')
  getCacheStatus() {
    const health = this.cacheService.getHealthStatus();
    const warmingStatus = this.cacheWarmingService.getWarmingStatus();

    return {
      status: health.status,
      uptime: `${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m ${Math.floor(process.uptime() % 60)}s`,
      totalEntries: this.cacheService.getCacheSize(),
      memoryUsage: {
        used: `${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(1)} MB`,
        percentage: Math.round(
          (process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) *
            100,
        ),
      },
      hitRate: health.hitRate,
      errorRate: health.errorRate,
      lastWarming: warmingStatus.lastWarming,
      activeKeys: this.cacheService.getAllKeys(),
    };
  }

  /**
   * Get cache health status and metrics
   */
  @Get('health')
  getCacheHealth() {
    return this.cacheMonitoringService.getCacheStatus();
  }

  /**
   * Get cache performance dashboard data
   */
  @Get('dashboard')
  getCacheDashboard() {
    return this.cacheMonitoringService.getDashboardData();
  }

  /**
   * Get cache metrics
   */
  @Get('metrics')
  getCacheMetrics() {
    return this.cacheService.getMetrics();
  }

  /**
   * Get cache performance history
   */
  @Get('performance/history')
  getPerformanceHistory(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : 20;
    return this.cacheMonitoringService.getPerformanceHistory(limitNum);
  }

  /**
   * Get cache alerts
   */
  @Get('alerts')
  getCacheAlerts(@Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : 20;
    return this.cacheMonitoringService.getAlertsHistory(limitNum);
  }

  /**
   * Resolve a cache alert
   */
  @Post('alerts/:alertId/resolve')
  resolveAlert(@Param('alertId') alertId: string) {
    const resolved = this.cacheMonitoringService.resolveAlert(alertId);
    return { success: resolved, alertId };
  }

  /**
   * Manual cache warming
   */
  @Post('warm')
  async warmCache(@Body() dto: CacheWarmingDto) {
    await this.cacheWarmingService.warmCacheManually(dto.keys);
    return { success: true, message: 'Cache warming initiated' };
  }

  /**
   * Get cache warming status
   */
  @Get('warming/status')
  getCacheWarmingStatus() {
    return this.cacheWarmingService.getWarmingStatus();
  }

  /**
   * Toggle cache warming for specific keys
   */
  @Post('warming/:key/toggle')
  toggleCacheWarming(
    @Param('key') key: string,
    @Body('enabled') enabled: boolean,
  ) {
    const success = this.cacheWarmingService.toggleCacheWarming(key, enabled);
    return { success, key, enabled };
  }

  /**
   * Clear specific cache keys
   */
  @Delete('keys')
  async clearCacheKeys(@Body() dto: CacheOperationDto) {
    if (dto.keys) {
      for (const key of dto.keys) {
        await this.cacheService.delete(key);
      }
      return { success: true, clearedKeys: dto.keys };
    }

    if (dto.pattern) {
      await this.cacheService.deleteByPattern(dto.pattern);
      return { success: true, pattern: dto.pattern };
    }

    return { success: false, message: 'No keys or pattern specified' };
  }

  /**
   * Clear all cache
   */
  @Delete('all')
  async clearAllCache(@Body() dto: CacheOperationDto) {
    if (dto.force) {
      await this.cacheService.clear();
      return { success: true, message: 'All cache cleared' };
    }
    return {
      success: false,
      message: 'Force flag required to clear all cache',
    };
  }

  /**
   * Reset cache metrics
   */
  @Post('metrics/reset')
  resetCacheMetrics() {
    this.cacheService.resetMetrics();
    return { success: true, message: 'Cache metrics reset' };
  }

  /**
   * Reset monitoring data
   */
  @Post('monitoring/reset')
  resetMonitoringData() {
    this.cacheMonitoringService.resetMonitoringData();
    return { success: true, message: 'Monitoring data reset' };
  }

  /**
   * Trigger manual cache performance monitoring
   */
  @Post('monitoring/check')
  triggerPerformanceCheck() {
    this.cacheMonitoringService.monitorCachePerformance();
    return { success: true, message: 'Performance check completed' };
  }

  /**
   * Get cache key information
   */
  @Get('keys/:key')
  async getCacheKeyInfo(@Param('key') key: string) {
    const value = await this.cacheService.get(key);
    return {
      key,
      exists: value !== null && value !== undefined,
      value: value
        ? typeof value === 'object'
          ? JSON.stringify(value)
          : value
        : null,
      type: typeof value,
    };
  }

  /**
   * Set cache key manually (for debugging)
   */
  @Post('keys/:key')
  async setCacheKey(
    @Param('key') key: string,
    @Body('value') value: unknown,
    @Body('ttl') ttl?: number,
  ) {
    await this.cacheService.set(key, value, ttl);
    return { success: true, key, value, ttl };
  }

  /**
   * Test cache performance
   */
  @Post('test/performance')
  async testCachePerformance(@Body('iterations') iterations?: number) {
    const iterationsCount = iterations || 100;
    const testKey = `test:performance:${Date.now()}`;
    const testValue = { data: 'test', timestamp: Date.now() };

    const startTime = Date.now();

    // Test write performance
    const writeStart = Date.now();
    for (let i = 0; i < iterationsCount; i++) {
      await this.cacheService.set(`${testKey}:${i}`, testValue, 60);
    }
    const writeTime = Date.now() - writeStart;

    // Test read performance
    const readStart = Date.now();
    for (let i = 0; i < iterationsCount; i++) {
      await this.cacheService.get(`${testKey}:${i}`);
    }
    const readTime = Date.now() - readStart;

    // Cleanup test keys
    for (let i = 0; i < iterationsCount; i++) {
      await this.cacheService.delete(`${testKey}:${i}`);
    }

    const totalTime = Date.now() - startTime;

    return {
      iterations: iterationsCount,
      performance: {
        totalTime: `${totalTime}ms`,
        writeTime: `${writeTime}ms`,
        readTime: `${readTime}ms`,
        avgWriteTime: `${(writeTime / iterationsCount).toFixed(2)}ms`,
        avgReadTime: `${(readTime / iterationsCount).toFixed(2)}ms`,
      },
    };
  }

  /**
   * Get cache statistics summary
   */
  @Get('stats')
  getCacheStats() {
    const metrics = this.cacheService.getMetrics();
    const health = this.cacheService.getHealthStatus();
    const warmingStatus = this.cacheWarmingService.getWarmingStatus();

    return {
      metrics,
      health,
      warming: warmingStatus,
      uptime: process.uptime(),
      timestamp: new Date(),
    };
  }

  /**
   * Export cache configuration and status
   */
  @Get('export')
  exportCacheData() {
    const metrics = this.cacheService.getMetrics();
    const health = this.cacheService.getHealthStatus();
    const dashboard = this.cacheMonitoringService.getDashboardData();
    const warmingStatus = this.cacheWarmingService.getWarmingStatus();

    return {
      exportTimestamp: new Date(),
      version: '2.0.0',
      metrics,
      health,
      dashboard,
      warming: warmingStatus,
      environment: process.env.NODE_ENV,
    };
  }
}

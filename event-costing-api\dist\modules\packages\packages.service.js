"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PackagesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PackagesService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../core/supabase/supabase.service");
const cache_service_1 = require("../../core/cache/cache.service");
const list_package_variations_dto_1 = require("./dto/list-package-variations.dto");
let PackagesService = PackagesService_1 = class PackagesService {
    supabaseService;
    cacheService;
    logger = new common_1.Logger(PackagesService_1.name);
    constructor(supabaseService, cacheService) {
        this.supabaseService = supabaseService;
        this.cacheService = cacheService;
    }
    async invalidatePackageCaches(packageId, action) {
        const relatedKeys = [
            'packages:popular',
            'packages-by-category:*',
            'package-options:*',
            'batch-package-options:*',
        ];
        await this.cacheService.invalidateWithRelationships('package', packageId, action, relatedKeys);
    }
    async findVariations(queryDto) {
        this.logger.log(`Finding package variations with query: ${JSON.stringify(queryDto)}`);
        const cacheKey = this.generatePackageVariationsCacheKey(queryDto);
        return this.cacheService.getOrSet(cacheKey, () => this.fetchPackageVariations(queryDto), 3600);
    }
    sortPackageVariations(packages, sortBy, sortOrder) {
        const sortMultiplier = sortOrder === list_package_variations_dto_1.SortDirection.ASC ? 1 : -1;
        return [...packages].sort((a, b) => {
            switch (sortBy) {
                case list_package_variations_dto_1.PackageSortField.NAME:
                    return sortMultiplier * a.name.localeCompare(b.name);
                case list_package_variations_dto_1.PackageSortField.PRICE:
                    return sortMultiplier * (a.price - b.price);
                case list_package_variations_dto_1.PackageSortField.CATEGORY:
                    return sortMultiplier * a.category_id.localeCompare(b.category_id);
                default:
                    return 0;
            }
        });
    }
    generatePackageVariationsCacheKey(queryDto) {
        const { categoryId, cityId, venueId, venueIds, currencyId, currentSelectionIds, search, sortBy, sortOrder, limit, offset, } = queryDto;
        const keyParts = [
            `categoryId:${categoryId || 'null'}`,
            `cityId:${cityId || 'null'}`,
            `venueId:${venueId || 'null'}`,
            `venueIds:${venueIds ? venueIds.sort().join(',') : 'null'}`,
            `currencyId:${currencyId}`,
            `currentSelectionIds:${currentSelectionIds ? currentSelectionIds.sort().join(',') : 'null'}`,
            `search:${search || 'null'}`,
            `sortBy:${sortBy || 'name'}`,
            `sortOrder:${sortOrder || 'asc'}`,
            `limit:${limit || 20}`,
            `offset:${offset || 0}`,
        ];
        return `package-variations:${keyParts.join(';')}`;
    }
    async fetchPackageVariations(queryDto) {
        const { categoryId, cityId, venueId, venueIds, currencyId, currentSelectionIds, search, sortBy = list_package_variations_dto_1.PackageSortField.NAME, sortOrder = list_package_variations_dto_1.SortDirection.ASC, limit = 20, offset = 0, } = queryDto;
        if (venueId && venueIds && venueIds.length > 0) {
            throw new common_1.BadRequestException('Cannot specify both venueId and venueIds. Use venueIds for multiple venues.');
        }
        const effectiveVenueIds = venueId ? [venueId] : venueIds;
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from('packages')
            .select(`
                id,
                name,
                description,
                category_id,
                quantity_basis,
                package_prices!inner(price, unit_base_cost)
            `, { count: 'exact' })
            .eq('is_deleted', false)
            .eq('package_prices.currency_id', currencyId);
        if (categoryId) {
            query = query.eq('category_id', categoryId);
        }
        if (search) {
            query = query.ilike('name', `%${search}%`);
        }
        const { data, error: pkgError, count, } = await query;
        if (pkgError) {
            this.logger.error(`Error fetching packages/prices for currency ${currencyId}: ${pkgError.message}`, pkgError.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve package variations.');
        }
        if (!data) {
            this.logger.error('Query returned null data for packagesWithPrices');
            throw new common_1.InternalServerErrorException('Failed to retrieve package variations (null data).');
        }
        const packagesWithPrices = data;
        if (!packagesWithPrices || packagesWithPrices.length === 0) {
            this.logger.log(`No packages found matching initial criteria (category/currency price).`);
            return {
                data: [],
                count: 0,
                limit,
                offset,
            };
        }
        const packageIds = packagesWithPrices.map(p => p.id);
        let cityAvailabilityMap = new Map();
        let venueAvailabilityMap = new Map();
        let conflictMap = new Map();
        if (cityId) {
            cityAvailabilityMap = await this.checkCityAvailability(supabase, packageIds, cityId);
        }
        if (effectiveVenueIds && effectiveVenueIds.length > 0) {
            venueAvailabilityMap = await this.checkVenuesAvailability(supabase, packageIds, effectiveVenueIds);
        }
        if (currentSelectionIds && currentSelectionIds.length > 0) {
            conflictMap = await this.checkConflicts(supabase, packageIds, currentSelectionIds);
        }
        const results = packagesWithPrices.map(pkg => {
            const priceData = pkg.package_prices[0];
            const isAvailableInCity = !cityId || cityAvailabilityMap.get(pkg.id) || false;
            const isAvailableInVenue = !effectiveVenueIds ||
                effectiveVenueIds.length === 0 ||
                venueAvailabilityMap.get(pkg.id) ||
                false;
            const conflicts = conflictMap.get(pkg.id) || false;
            return {
                package_id: pkg.id,
                name: pkg.name,
                description: pkg.description,
                category_id: pkg.category_id,
                quantity_basis: pkg.quantity_basis,
                price: priceData.price,
                unit_base_cost: priceData.unit_base_cost,
                is_available_in_city: isAvailableInCity,
                is_available_in_venue: isAvailableInVenue,
                conflicts_with_selection: conflicts,
            };
        });
        let filteredResults = results;
        if (cityId) {
            filteredResults = filteredResults.filter(r => r.is_available_in_city);
        }
        if (effectiveVenueIds && effectiveVenueIds.length > 0) {
            filteredResults = filteredResults.filter(r => r.is_available_in_venue);
        }
        const sortedResults = this.sortPackageVariations(filteredResults, sortBy, sortOrder);
        const paginatedResults = sortedResults.slice(offset, offset + limit);
        this.logger.log(`Found ${filteredResults.length} package variations matching criteria. Returning ${paginatedResults.length} items.`);
        return {
            data: paginatedResults,
            count: filteredResults.length,
            limit,
            offset,
        };
    }
    async findOptions(packageId, currencyId, venueId) {
        this.logger.log(`Fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}`);
        const cacheKey = `package-options:${packageId}:${currencyId || 'all'}:${venueId || 'all'}`;
        return this.cacheService.getOrSet(cacheKey, () => this.fetchPackageOptions(packageId, currencyId, venueId), 3600);
    }
    async fetchPackageOptions(packageId, currencyId, venueId) {
        const supabase = this.supabaseService.getClient();
        let query = supabase
            .from('package_options')
            .select('id, option_name, description, price_adjustment, cost_adjustment')
            .eq('applicable_package_id', packageId);
        if (currencyId) {
            query = query.eq('currency_id', currencyId);
        }
        if (venueId) {
            query = query.eq('venue_id', venueId);
        }
        query = query.order('option_name');
        const { data, error } = await query;
        if (error) {
            this.logger.error(`Error fetching options for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve package options.');
        }
        if (!data || data.length === 0) {
            this.logger.log(`No active options found for package ${packageId}${currencyId ? `, currency ${currencyId}` : ''}${venueId ? `, venue ${venueId}` : ''}`);
            return [];
        }
        return data;
    }
    async checkCityAvailability(supabase, packageIds, cityId) {
        const availabilityMap = new Map();
        packageIds.forEach(id => availabilityMap.set(id, false));
        const { data, error } = await supabase
            .from('package_cities')
            .select('package_id')
            .in('package_id', packageIds)
            .eq('city_id', cityId)
            .returns();
        if (error) {
            this.logger.error(`Error checking city availability for city ${cityId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed check package city availability.');
        }
        data?.forEach(row => {
            availabilityMap.set(row.package_id, true);
        });
        return availabilityMap;
    }
    async checkVenueAvailability(supabase, packageIds, venueId) {
        const availabilityMap = new Map();
        packageIds.forEach(id => availabilityMap.set(id, false));
        const { data, error } = await supabase
            .from('package_venues')
            .select('package_id')
            .in('package_id', packageIds)
            .eq('venue_id', venueId)
            .returns();
        if (error) {
            this.logger.error(`Error checking venue availability for venue ${venueId}: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed check package venue availability.');
        }
        data?.forEach(row => {
            availabilityMap.set(row.package_id, true);
        });
        return availabilityMap;
    }
    async checkVenuesAvailability(supabase, packageIds, venueIds) {
        const availabilityMap = new Map();
        packageIds.forEach(id => availabilityMap.set(id, false));
        if (venueIds.length === 0) {
            return availabilityMap;
        }
        const { data, error } = await supabase
            .from('package_venues')
            .select('package_id, venue_id')
            .in('package_id', packageIds)
            .in('venue_id', venueIds)
            .returns();
        if (error) {
            this.logger.error(`Error checking venue availability for venues [${venueIds.join(', ')}]: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed to check package venue availability.');
        }
        data?.forEach(row => {
            availabilityMap.set(row.package_id, true);
        });
        return availabilityMap;
    }
    async checkConflicts(supabase, packageIds, currentSelectionIds) {
        const conflictMap = new Map();
        packageIds.forEach(id => conflictMap.set(id, false));
        const { data, error } = await supabase
            .from('package_dependencies')
            .select('package_id, depends_on_package_id')
            .or(`and(package_id.in.(${packageIds.join(',')}),depends_on_package_id.in.(${currentSelectionIds.join(',')})),and(depends_on_package_id.in.(${packageIds.join(',')}),package_id.in.(${currentSelectionIds.join(',')}))`)
            .returns();
        if (error) {
            this.logger.error(`Error checking package conflicts: ${error.message}`, error.stack);
            throw new common_1.InternalServerErrorException('Failed check package conflicts.');
        }
        data?.forEach(dep => {
            if (dep.package_id &&
                packageIds.includes(dep.package_id) &&
                currentSelectionIds.includes(dep.depends_on_package_id)) {
                conflictMap.set(dep.package_id, true);
            }
            if (dep.depends_on_package_id &&
                packageIds.includes(dep.depends_on_package_id) &&
                currentSelectionIds.includes(dep.package_id))
                conflictMap.set(dep.depends_on_package_id, true);
        });
        return conflictMap;
    }
    async getPackagesByCategory(currencyId, cityId, venueId, includeOptions = false) {
        this.logger.log(`Getting packages by category for currency ${currencyId}, city ${cityId || 'any'}, venue ${venueId || 'any'}`);
        const cacheKey = `packages-by-category:${currencyId}:${cityId || 'all'}:${venueId || 'all'}:${includeOptions}`;
        return this.cacheService.getOrSet(cacheKey, () => this.fetchPackagesByCategory(currencyId, cityId, venueId, includeOptions), 3600);
    }
    async fetchPackagesByCategory(currencyId, cityId, venueId, includeOptions = false) {
        const supabase = this.supabaseService.getClient();
        const { data: packages, error: packageError } = await supabase.rpc('get_packages_by_category_with_availability', {
            p_currency_id: currencyId,
            p_city_id: cityId,
            p_venue_id: venueId,
            p_category_id: null,
        });
        if (packageError) {
            this.logger.error(`Error fetching packages: ${packageError.message}`, packageError.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve packages.');
        }
        const packagesByCategory = this.organizePackagesByCategory(packages);
        if (includeOptions && packages.length > 0) {
            await this.addOptionsToPackages(packagesByCategory, currencyId, venueId);
        }
        return {
            categories: packagesByCategory,
        };
    }
    organizePackagesByCategory(packages) {
        const categoryMap = new Map();
        packages.forEach(pkg => {
            const categoryId = pkg.category_id || 'uncategorized';
            if (!categoryMap.has(categoryId)) {
                categoryMap.set(categoryId, {
                    id: categoryId,
                    name: pkg.category_name || 'Uncategorized',
                    display_order: pkg.category_display_order || 9999,
                    packages: [],
                });
            }
            const category = categoryMap.get(categoryId);
            if (category) {
                category.packages.push({
                    id: pkg.id,
                    name: pkg.name,
                    description: pkg.description,
                    quantity_basis: pkg.quantity_basis,
                    price: pkg.price,
                    unit_base_cost: pkg.unit_base_cost,
                });
            }
        });
        return Array.from(categoryMap.values()).sort((a, b) => a.display_order - b.display_order);
    }
    async addOptionsToPackages(categories, currencyId, venueId) {
        const packageIds = categories.flatMap(cat => cat.packages.map(pkg => pkg.id));
        if (packageIds.length === 0) {
            return;
        }
        const options = await this.getBatchPackageOptions(packageIds, currencyId, venueId);
        categories.forEach(category => {
            category.packages.forEach(pkg => {
                pkg.options = options.options[pkg.id] || [];
            });
        });
    }
    async getBatchPackageOptions(packageIds, currencyId, venueId) {
        this.logger.log(`Getting batch options for ${packageIds.length} packages, currency ${currencyId}, venue ${venueId || 'any'}`);
        const cacheKey = `batch-package-options:${packageIds.join(',')}:${currencyId}:${venueId || 'all'}`;
        return this.cacheService.getOrSet(cacheKey, () => this.fetchBatchPackageOptions(packageIds, currencyId, venueId), 3600);
    }
    async fetchBatchPackageOptions(packageIds, currencyId, venueId) {
        const supabase = this.supabaseService.getClient();
        const { data: options, error: optionsError } = await supabase.rpc('get_batch_package_options', {
            p_package_ids: packageIds,
            p_currency_id: currencyId,
            p_venue_id: venueId,
        });
        if (optionsError) {
            this.logger.error(`Error fetching batch options: ${optionsError.message}`, optionsError.stack);
            throw new common_1.InternalServerErrorException('Failed to retrieve package options.');
        }
        const optionsByPackage = {};
        options.forEach(option => {
            if (!optionsByPackage[option.package_id]) {
                optionsByPackage[option.package_id] = [];
            }
            optionsByPackage[option.package_id].push({
                id: option.option_id,
                option_name: option.option_name,
                description: option.description,
                price_adjustment: option.price_adjustment,
                cost_adjustment: option.cost_adjustment,
                is_default_for_package: option.is_default_for_package,
                is_required: option.is_required,
            });
        });
        return {
            options: optionsByPackage,
        };
    }
};
exports.PackagesService = PackagesService;
exports.PackagesService = PackagesService = PackagesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        cache_service_1.CacheService])
], PackagesService);
//# sourceMappingURL=packages.service.js.map
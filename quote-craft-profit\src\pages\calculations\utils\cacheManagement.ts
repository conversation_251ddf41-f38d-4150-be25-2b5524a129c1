/**
 * Cache Management Utilities
 * 
 * Phase 4 Performance Optimization: Utilities for managing validation cache
 * and optimizing cache performance across the application.
 */

import { validationCache, type CacheStatistics } from '@/lib/validationCache';

/**
 * Cache management operations interface
 */
export interface CacheManagementOperations {
  /** Clear all cache entries */
  clearAll: () => void;
  
  /** Preload common calculation UUIDs */
  preloadCalculationUUIDs: (calculationIds: string[]) => void;
  
  /** Get cache performance report */
  getPerformanceReport: () => CachePerformanceReport;
  
  /** Optimize cache settings based on usage patterns */
  optimizeCache: () => CacheOptimizationResult;
  
  /** Export cache statistics for analysis */
  exportStatistics: () => string;
  
  /** Import and preload UUIDs from calculation list */
  preloadFromCalculationList: (calculations: Array<{ id: string }>) => void;
}

/**
 * Cache performance report interface
 */
export interface CachePerformanceReport {
  /** Overall cache performance score (0-100) */
  performanceScore: number;
  
  /** Current cache statistics */
  statistics: CacheStatistics;
  
  /** Performance insights */
  insights: CacheInsight[];
  
  /** Optimization recommendations */
  recommendations: CacheRecommendation[];
  
  /** Cache health status */
  healthStatus: 'excellent' | 'good' | 'fair' | 'poor';
  
  /** Estimated performance improvement potential */
  improvementPotential: number;
}

/**
 * Cache insight interface
 */
export interface CacheInsight {
  /** Insight type */
  type: 'efficiency' | 'usage' | 'memory' | 'performance';
  
  /** Insight message */
  message: string;
  
  /** Severity level */
  severity: 'info' | 'warning' | 'error';
  
  /** Metric value associated with insight */
  value?: number;
  
  /** Unit for the metric value */
  unit?: string;
}

/**
 * Cache recommendation interface
 */
export interface CacheRecommendation {
  /** Recommendation title */
  title: string;
  
  /** Detailed description */
  description: string;
  
  /** Priority level */
  priority: 'low' | 'medium' | 'high' | 'critical';
  
  /** Estimated impact */
  impact: 'minor' | 'moderate' | 'significant' | 'major';
  
  /** Implementation difficulty */
  difficulty: 'easy' | 'medium' | 'hard';
  
  /** Action to take */
  action?: () => void;
}

/**
 * Cache optimization result interface
 */
export interface CacheOptimizationResult {
  /** Whether optimization was applied */
  optimized: boolean;
  
  /** Changes made during optimization */
  changes: string[];
  
  /** Expected performance improvement */
  expectedImprovement: number;
  
  /** New cache configuration */
  newConfiguration?: {
    maxSize: number;
    ttl: number;
  };
}

/**
 * Create cache management operations
 * 
 * @returns Cache management operations object
 */
export const createCacheManagement = (): CacheManagementOperations => {
  return {
    clearAll: () => {
      validationCache.clear();
      console.log('[Cache Management] All cache entries cleared');
    },

    preloadCalculationUUIDs: (calculationIds: string[]) => {
      const validIds = calculationIds.filter(id => id && typeof id === 'string');
      validationCache.preload(validIds);
      console.log(`[Cache Management] Preloaded ${validIds.length} calculation UUIDs`);
    },

    getPerformanceReport: () => {
      const stats = validationCache.getStatistics();
      return generatePerformanceReport(stats);
    },

    optimizeCache: () => {
      const stats = validationCache.getStatistics();
      return optimizeCacheSettings(stats);
    },

    exportStatistics: () => {
      const stats = validationCache.getStatistics();
      const report = generatePerformanceReport(stats);
      
      return JSON.stringify({
        timestamp: new Date().toISOString(),
        statistics: stats,
        report: report,
        entries: validationCache.getEntries(),
      }, null, 2);
    },

    preloadFromCalculationList: (calculations: Array<{ id: string }>) => {
      const ids = calculations
        .map(calc => calc.id)
        .filter(id => id && typeof id === 'string');
      
      validationCache.preload(ids);
      console.log(`[Cache Management] Preloaded ${ids.length} UUIDs from calculation list`);
    },
  };
};

/**
 * Generate comprehensive performance report
 * 
 * @param stats - Current cache statistics
 * @returns Performance report
 */
function generatePerformanceReport(stats: CacheStatistics): CachePerformanceReport {
  const performanceScore = calculatePerformanceScore(stats);
  const insights = generateInsights(stats);
  const recommendations = generateRecommendations(stats);
  const healthStatus = calculateHealthStatus(stats);
  const improvementPotential = calculateImprovementPotential(stats);

  return {
    performanceScore,
    statistics: stats,
    insights,
    recommendations,
    healthStatus,
    improvementPotential,
  };
}

/**
 * Calculate overall performance score
 * 
 * @param stats - Cache statistics
 * @returns Performance score (0-100)
 */
function calculatePerformanceScore(stats: CacheStatistics): number {
  if (stats.totalValidations === 0) return 50; // Neutral score for no data

  // Weight different factors
  const hitRateScore = stats.hitRate; // 0-100
  const memoryEfficiencyScore = (1 - (stats.size / stats.maxSize)) * 100; // 0-100
  const performanceScore = Math.min(100, (1 / Math.max(0.1, stats.averageValidationTime)) * 10); // 0-100

  // Weighted average
  const totalScore = (
    hitRateScore * 0.5 +
    memoryEfficiencyScore * 0.3 +
    performanceScore * 0.2
  );

  return Math.round(totalScore);
}

/**
 * Generate cache insights
 * 
 * @param stats - Cache statistics
 * @returns Array of insights
 */
function generateInsights(stats: CacheStatistics): CacheInsight[] {
  const insights: CacheInsight[] = [];

  // Efficiency insights
  if (stats.hitRate > 80) {
    insights.push({
      type: 'efficiency',
      message: 'Excellent cache hit rate indicates optimal UUID reuse patterns',
      severity: 'info',
      value: stats.hitRate,
      unit: '%',
    });
  } else if (stats.hitRate < 30) {
    insights.push({
      type: 'efficiency',
      message: 'Low cache hit rate suggests diverse UUID patterns or insufficient cache size',
      severity: 'warning',
      value: stats.hitRate,
      unit: '%',
    });
  }

  // Usage insights
  if (stats.totalValidations > 100) {
    insights.push({
      type: 'usage',
      message: 'High validation volume indicates cache is providing significant performance benefit',
      severity: 'info',
      value: stats.totalValidations,
      unit: 'validations',
    });
  }

  // Memory insights
  const memoryUsageRatio = stats.size / stats.maxSize;
  if (memoryUsageRatio > 0.9) {
    insights.push({
      type: 'memory',
      message: 'Cache is nearly full, consider increasing size or reducing TTL',
      severity: 'warning',
      value: memoryUsageRatio * 100,
      unit: '%',
    });
  } else if (memoryUsageRatio < 0.1 && stats.totalValidations > 50) {
    insights.push({
      type: 'memory',
      message: 'Cache is underutilized, consider reducing size to save memory',
      severity: 'info',
      value: memoryUsageRatio * 100,
      unit: '%',
    });
  }

  // Performance insights
  if (stats.averageValidationTime > 1) {
    insights.push({
      type: 'performance',
      message: 'Validation operations are taking significant time, cache provides good benefit',
      severity: 'info',
      value: stats.averageValidationTime,
      unit: 'ms',
    });
  }

  return insights;
}

/**
 * Generate optimization recommendations
 * 
 * @param stats - Cache statistics
 * @returns Array of recommendations
 */
function generateRecommendations(stats: CacheStatistics): CacheRecommendation[] {
  const recommendations: CacheRecommendation[] = [];

  // Hit rate recommendations
  if (stats.hitRate < 40 && stats.totalValidations > 20) {
    recommendations.push({
      title: 'Increase Cache Size',
      description: 'Low hit rate with significant usage suggests cache size is insufficient',
      priority: 'high',
      impact: 'significant',
      difficulty: 'easy',
    });
  }

  // Memory optimization
  if (stats.size / stats.maxSize > 0.9) {
    recommendations.push({
      title: 'Expand Cache Capacity',
      description: 'Cache is nearly full, increasing size will prevent premature evictions',
      priority: 'medium',
      impact: 'moderate',
      difficulty: 'easy',
    });
  }

  // Performance optimization
  if (stats.averageValidationTime < 0.1 && stats.hitRate > 80) {
    recommendations.push({
      title: 'Consider Reducing Cache Size',
      description: 'Fast validations with high hit rate suggest cache could be smaller',
      priority: 'low',
      impact: 'minor',
      difficulty: 'easy',
    });
  }

  return recommendations;
}

/**
 * Calculate health status
 * 
 * @param stats - Cache statistics
 * @returns Health status
 */
function calculateHealthStatus(stats: CacheStatistics): 'excellent' | 'good' | 'fair' | 'poor' {
  const score = calculatePerformanceScore(stats);
  
  if (score >= 80) return 'excellent';
  if (score >= 60) return 'good';
  if (score >= 40) return 'fair';
  return 'poor';
}

/**
 * Calculate improvement potential
 * 
 * @param stats - Cache statistics
 * @returns Improvement potential percentage
 */
function calculateImprovementPotential(stats: CacheStatistics): number {
  if (stats.totalValidations === 0) return 0;
  
  const currentScore = calculatePerformanceScore(stats);
  const maxPossibleScore = 100;
  
  return Math.round(maxPossibleScore - currentScore);
}

/**
 * Optimize cache settings based on usage patterns
 * 
 * @param stats - Current cache statistics
 * @returns Optimization result
 */
function optimizeCacheSettings(stats: CacheStatistics): CacheOptimizationResult {
  const changes: string[] = [];
  let optimized = false;
  let expectedImprovement = 0;

  // This is a placeholder for actual optimization logic
  // In a real implementation, this would adjust cache settings
  
  if (stats.hitRate < 40 && stats.size / stats.maxSize > 0.8) {
    changes.push('Recommended: Increase cache size from 100 to 150 entries');
    expectedImprovement += 15;
    optimized = true;
  }

  if (stats.averageValidationTime < 0.1 && stats.hitRate > 90) {
    changes.push('Recommended: Reduce cache size to save memory');
    expectedImprovement += 5;
    optimized = true;
  }

  return {
    optimized,
    changes,
    expectedImprovement,
    newConfiguration: optimized ? {
      maxSize: 150,
      ttl: 5 * 60 * 1000,
    } : undefined,
  };
}

// Export singleton instance
export const cacheManagement = createCacheManagement();

export default cacheManagement;

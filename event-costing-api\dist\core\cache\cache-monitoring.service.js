"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var CacheMonitoringService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheMonitoringService = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
let CacheMonitoringService = CacheMonitoringService_1 = class CacheMonitoringService {
    cacheService;
    logger = new common_1.Logger(CacheMonitoringService_1.name);
    alerts = [];
    performanceHistory = [];
    maxHistorySize = 100;
    maxAlertsSize = 50;
    constructor(cacheService) {
        this.cacheService = cacheService;
    }
    monitorCachePerformance() {
        try {
            const metrics = this.cacheService.getMetrics();
            const health = this.cacheService.getHealthStatus();
            const report = {
                timestamp: new Date(),
                period: '5min',
                metrics,
                health,
                alerts: [],
                recommendations: this.generateRecommendations(metrics, health),
            };
            const newAlerts = this.checkForAlerts(metrics, health);
            report.alerts = newAlerts;
            this.alerts.push(...newAlerts);
            this.trimAlerts();
            this.performanceHistory.push(report);
            this.trimHistory();
            this.logger.debug(`Cache Performance - Hit Rate: ${health.hitRate}%, Error Rate: ${health.errorRate}%, Status: ${health.status}`);
            this.logger.debug('Cache performance report generated', { report });
            if (newAlerts.some(alert => alert.severity === 'critical')) {
                this.handleCriticalAlerts(newAlerts.filter(alert => alert.severity === 'critical'));
            }
        }
        catch (error) {
            this.logger.error('Error monitoring cache performance:', error);
        }
    }
    handleCacheInvalidation(event) {
        this.logger.debug(`Cache invalidation detected: ${event.domain}:${event.id} (${event.action})`);
    }
    checkForAlerts(metrics, health) {
        const alerts = [];
        const now = new Date();
        if (health.hitRate < 30) {
            alerts.push({
                id: `hit-rate-${now.getTime()}`,
                type: 'performance',
                severity: 'critical',
                message: `Cache hit rate critically low: ${health.hitRate}%`,
                timestamp: now,
                metrics: { hitRate: health.hitRate },
            });
        }
        else if (health.hitRate < 60) {
            alerts.push({
                id: `hit-rate-${now.getTime()}`,
                type: 'performance',
                severity: 'medium',
                message: `Cache hit rate below optimal: ${health.hitRate}%`,
                timestamp: now,
                metrics: { hitRate: health.hitRate },
            });
        }
        if (health.errorRate > 10) {
            alerts.push({
                id: `error-rate-${now.getTime()}`,
                type: 'error',
                severity: 'critical',
                message: `Cache error rate critically high: ${health.errorRate}%`,
                timestamp: now,
                metrics: { errorRate: health.errorRate },
            });
        }
        else if (health.errorRate > 5) {
            alerts.push({
                id: `error-rate-${now.getTime()}`,
                type: 'error',
                severity: 'high',
                message: `Cache error rate elevated: ${health.errorRate}%`,
                timestamp: now,
                metrics: { errorRate: health.errorRate },
            });
        }
        const totalOps = metrics.hits + metrics.misses + metrics.sets + metrics.deletes;
        if (totalOps > 10000) {
            alerts.push({
                id: `high-activity-${now.getTime()}`,
                type: 'capacity',
                severity: 'medium',
                message: `High cache activity detected: ${totalOps} operations`,
                timestamp: now,
                metrics: { totalOperations: totalOps },
            });
        }
        return alerts;
    }
    generateRecommendations(metrics, health) {
        const recommendations = [];
        if (health.hitRate < 70) {
            recommendations.push('Consider increasing TTL for frequently accessed data');
            recommendations.push('Review cache key strategies for better hit rates');
        }
        if (health.errorRate > 2) {
            recommendations.push('Investigate cache connection issues');
            recommendations.push('Review error handling in cache operations');
        }
        const totalOps = metrics.hits + metrics.misses;
        if (totalOps > 0 && metrics.misses / totalOps > 0.5) {
            recommendations.push('High cache miss rate - consider cache warming strategies');
        }
        if (metrics.deletes > metrics.sets * 0.8) {
            recommendations.push('High cache invalidation rate - review invalidation strategy');
        }
        return recommendations;
    }
    handleCriticalAlerts(criticalAlerts) {
        for (const alert of criticalAlerts) {
            this.logger.error(`CRITICAL CACHE ALERT: ${alert.message}`);
            this.logger.error('CRITICAL CACHE ALERT EMITTED', { alert });
        }
    }
    getCacheStatus() {
        const health = this.cacheService.getHealthStatus();
        const metrics = this.cacheService.getMetrics();
        const activeAlerts = this.alerts.filter(alert => !alert.resolved);
        const recentRecommendations = this.performanceHistory
            .slice(-5)
            .flatMap(report => report.recommendations)
            .filter((rec, index, arr) => arr.indexOf(rec) === index);
        return {
            health,
            metrics,
            activeAlerts,
            recentRecommendations,
        };
    }
    getPerformanceHistory(limit = 20) {
        return this.performanceHistory.slice(-limit);
    }
    getAlertsHistory(limit = 20) {
        return this.alerts.slice(-limit);
    }
    resolveAlert(alertId) {
        const alert = this.alerts.find(a => a.id === alertId);
        if (alert) {
            alert.resolved = true;
            this.logger.log(`Alert resolved: ${alertId}`);
            return true;
        }
        return false;
    }
    getDashboardData() {
        const currentHealth = this.cacheService.getHealthStatus();
        const metrics = this.cacheService.getMetrics();
        const recentReports = this.performanceHistory.slice(-10);
        const hitRateTrend = recentReports.map(r => r.health.hitRate);
        const errorRateTrend = recentReports.map(r => r.health.errorRate);
        const operationsTrend = recentReports.map(r => r.metrics.hits + r.metrics.misses + r.metrics.sets + r.metrics.deletes);
        const activeAlerts = this.alerts.filter(a => !a.resolved);
        const alertCounts = {
            critical: activeAlerts.filter(a => a.severity === 'critical').length,
            high: activeAlerts.filter(a => a.severity === 'high').length,
            medium: activeAlerts.filter(a => a.severity === 'medium').length,
            low: activeAlerts.filter(a => a.severity === 'low').length,
        };
        return {
            currentHealth,
            metrics,
            trends: {
                hitRateTrend,
                errorRateTrend,
                operationsTrend,
            },
            alerts: alertCounts,
        };
    }
    trimAlerts() {
        if (this.alerts.length > this.maxAlertsSize) {
            this.alerts = this.alerts.slice(-this.maxAlertsSize);
        }
    }
    trimHistory() {
        if (this.performanceHistory.length > this.maxHistorySize) {
            this.performanceHistory = this.performanceHistory.slice(-this.maxHistorySize);
        }
    }
    resetMonitoringData() {
        this.alerts = [];
        this.performanceHistory = [];
        this.cacheService.resetMetrics();
        this.logger.log('Cache monitoring data reset');
    }
};
exports.CacheMonitoringService = CacheMonitoringService;
exports.CacheMonitoringService = CacheMonitoringService = CacheMonitoringService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [cache_service_1.CacheService])
], CacheMonitoringService);
//# sourceMappingURL=cache-monitoring.service.js.map
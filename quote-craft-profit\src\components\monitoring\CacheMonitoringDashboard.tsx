import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Activity,
  Database,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  BarChart3,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

interface CacheHealth {
  status: "healthy" | "degraded" | "unhealthy";
  hitRate: number;
  errorRate: number;
}

interface CacheStatus {
  status: string;
  uptime: string;
  totalEntries: number;
  memoryUsage: {
    used: string;
    percentage: number;
  };
  hitRate: number;
  errorRate: number;
  lastWarming: string;
  activeKeys: string[];
}

interface CacheAlert {
  id: string;
  type: "performance" | "error" | "capacity" | "health";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  timestamp: string;
}

// API functions for cache monitoring
const API_BASE_URL =
  import.meta.env.VITE_EVENT_COSTING_API_URL || "http://localhost:5000";

const fetchCacheStatus = async (): Promise<CacheStatus> => {
  const response = await fetch(`${API_BASE_URL}/cache/status`);
  if (!response.ok) throw new Error("Failed to fetch cache status");
  return response.json();
};

const fetchCacheMetrics = async (): Promise<{
  metrics: CacheMetrics;
  performance: any;
  topKeys: any[];
}> => {
  const response = await fetch(`${API_BASE_URL}/cache/metrics`);
  if (!response.ok) throw new Error("Failed to fetch cache metrics");
  return response.json();
};

const fetchCacheHealth = async (): Promise<CacheHealth> => {
  const response = await fetch(`${API_BASE_URL}/cache/health`);
  if (!response.ok) throw new Error("Failed to fetch cache health");
  return response.json();
};

const triggerCacheCheck = async (): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/cache/monitoring/check`, {
    method: "POST",
  });
  if (!response.ok) throw new Error("Failed to trigger cache check");
};

export const CacheMonitoringDashboard: React.FC = () => {
  const [refreshInterval, setRefreshInterval] = useState(30000); // 30 seconds

  // Fetch cache data with auto-refresh
  const {
    data: cacheStatus,
    isLoading: statusLoading,
    refetch: refetchStatus,
  } = useQuery({
    queryKey: ["cache", "status"],
    queryFn: fetchCacheStatus,
    refetchInterval: refreshInterval,
    retry: 2,
  });

  const {
    data: cacheMetrics,
    isLoading: metricsLoading,
    refetch: refetchMetrics,
  } = useQuery({
    queryKey: ["cache", "metrics"],
    queryFn: fetchCacheMetrics,
    refetchInterval: refreshInterval,
    retry: 2,
  });

  const {
    data: cacheHealth,
    isLoading: healthLoading,
    refetch: refetchHealth,
  } = useQuery({
    queryKey: ["cache", "health"],
    queryFn: fetchCacheHealth,
    refetchInterval: refreshInterval,
    retry: 2,
  });

  const handleManualRefresh = () => {
    refetchStatus();
    refetchMetrics();
    refetchHealth();
  };

  const handleTriggerCheck = async () => {
    try {
      await triggerCacheCheck();
      // Refresh data after triggering check
      setTimeout(() => {
        handleManualRefresh();
      }, 1000);
    } catch (error) {
      console.error("Failed to trigger cache check:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "bg-green-500";
      case "degraded":
        return "bg-yellow-500";
      case "unhealthy":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "destructive";
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "outline";
    }
  };

  if (statusLoading || metricsLoading || healthLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading cache monitoring data...
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cache Monitoring Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time cache performance and health monitoring
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleManualRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleTriggerCheck}>
            <Activity className="h-4 w-4 mr-2" />
            Run Check
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cache Status</CardTitle>
            <div
              className={`h-3 w-3 rounded-full ${getStatusColor(
                cacheStatus?.status || "unknown"
              )}`}
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold capitalize">
              {cacheStatus?.status || "Unknown"}
            </div>
            <p className="text-xs text-muted-foreground">
              Uptime: {cacheStatus?.uptime || "N/A"}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStatus?.hitRate?.toFixed(1) || "0"}%
            </div>
            <Progress value={cacheStatus?.hitRate || 0} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStatus?.memoryUsage?.used || "N/A"}
            </div>
            <Progress
              value={cacheStatus?.memoryUsage?.percentage || 0}
              className="mt-2"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {cacheStatus?.totalEntries || 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Active cache entries
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <Tabs defaultValue="metrics" className="space-y-4">
        <TabsList>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="keys">Active Keys</TabsTrigger>
          <TabsTrigger value="health">Health</TabsTrigger>
        </TabsList>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Cache Operations</CardTitle>
                <CardDescription>
                  Total cache operations breakdown
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Hits</span>
                  <Badge variant="secondary">
                    {cacheMetrics?.metrics?.hits || 0}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Misses</span>
                  <Badge variant="outline">
                    {cacheMetrics?.metrics?.misses || 0}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Sets</span>
                  <Badge variant="secondary">
                    {cacheMetrics?.metrics?.sets || 0}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Deletes</span>
                  <Badge variant="outline">
                    {cacheMetrics?.metrics?.deletes || 0}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Errors</span>
                  <Badge variant="destructive">
                    {cacheMetrics?.metrics?.errors || 0}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Cache performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Hit Rate</span>
                  <Badge variant="secondary">
                    {cacheMetrics?.performance?.hitRate?.toFixed(1) || "0"}%
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Error Rate</span>
                  <Badge
                    variant={
                      cacheMetrics?.performance?.errorRate > 5
                        ? "destructive"
                        : "secondary"
                    }
                  >
                    {cacheMetrics?.performance?.errorRate?.toFixed(1) || "0"}%
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Avg Response Time</span>
                  <Badge variant="secondary">
                    {cacheMetrics?.performance?.averageResponseTime || "N/A"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>Total Requests</span>
                  <Badge variant="secondary">
                    {cacheMetrics?.performance?.totalRequests || 0}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="keys" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Cache Keys</CardTitle>
              <CardDescription>Currently cached data keys</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {cacheStatus?.activeKeys?.map((key, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="justify-start"
                  >
                    {key}
                  </Badge>
                )) || (
                  <p className="text-muted-foreground">No active keys found</p>
                )}
              </div>
            </CardContent>
          </Card>

          {cacheMetrics?.topKeys && cacheMetrics.topKeys.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Keys</CardTitle>
                <CardDescription>
                  Most frequently accessed cache keys
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {cacheMetrics.topKeys.map((keyData, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 border rounded"
                    >
                      <div>
                        <p className="font-medium">{keyData.key}</p>
                        <p className="text-sm text-muted-foreground">
                          Last accessed:{" "}
                          {new Date(keyData.lastAccessed).toLocaleString()}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary">{keyData.hits} hits</Badge>
                        <p className="text-sm text-muted-foreground mt-1">
                          TTL: {Math.round(keyData.ttl / 60)}m
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cache Health Status</CardTitle>
              <CardDescription>Overall cache system health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  {cacheHealth?.status === "healthy" ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <AlertTriangle className="h-5 w-5 text-yellow-500" />
                  )}
                  <span className="font-medium capitalize">
                    {cacheHealth?.status || "Unknown"}
                  </span>
                </div>

                {cacheHealth?.hitRate && cacheHealth.hitRate < 70 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Cache hit rate is below optimal (70%). Current:{" "}
                      {cacheHealth.hitRate.toFixed(1)}%
                    </AlertDescription>
                  </Alert>
                )}

                {cacheHealth?.errorRate && cacheHealth.errorRate > 2 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Cache error rate is elevated. Current:{" "}
                      {cacheHealth.errorRate.toFixed(1)}%
                    </AlertDescription>
                  </Alert>
                )}

                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <p className="text-sm font-medium">Hit Rate</p>
                    <p className="text-2xl font-bold">
                      {cacheHealth?.hitRate?.toFixed(1) || "0"}%
                    </p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Error Rate</p>
                    <p className="text-2xl font-bold">
                      {cacheHealth?.errorRate?.toFixed(1) || "0"}%
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CacheMonitoringDashboard;

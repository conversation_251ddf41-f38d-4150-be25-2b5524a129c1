import { OnModuleInit } from '@nestjs/common';
import { CacheService } from './cache.service';
import { SupabaseService } from '../supabase/supabase.service';
export interface CacheWarmingConfig {
    key: string;
    factory: () => Promise<any>;
    ttl?: number;
    priority: number;
    schedule?: string;
    enabled: boolean;
}
export declare class CacheWarmingService implements OnModuleInit {
    private readonly cacheService;
    private readonly supabaseService;
    private readonly logger;
    private warmingConfigs;
    constructor(cacheService: CacheService, supabaseService: SupabaseService);
    onModuleInit(): Promise<void>;
    private initializeWarmingConfigs;
    private performInitialWarming;
    scheduledCacheWarming(): Promise<void>;
    warmCacheManually(keys?: string[]): Promise<void>;
    private warmCategories;
    private warmCities;
    private warmCurrencies;
    private warmPopularPackages;
    private warmDivisions;
    private warmPopularVenues;
    private warmEventTypes;
    getWarmingStatus(): {
        totalConfigs: number;
        enabledConfigs: number;
        lastWarming: Date | null;
        nextScheduledWarming: Date | null;
    };
    toggleCacheWarming(key: string, enabled: boolean): boolean;
}

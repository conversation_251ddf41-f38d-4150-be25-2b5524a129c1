/**
 * Calculation Mutation Service
 * PHASE 2 OPTIMIZATION: Consolidated service for all calculation mutations
 *
 * This service handles all calculation mutation operations:
 * - Line item operations (add, update, remove)
 * - Custom item operations
 * - Package item operations
 * - Calculation recalculation
 * - Update operations
 *
 * Consolidates functionality from line-items/lineItemService.ts and update operations
 */

import { supabase } from "@/integrations/supabase/client";
import { getAuthenticatedApiClient } from "@/integrations/api/client";
import { API_ENDPOINTS } from "@/integrations/api/endpoints";
import {
  LineItem,
  LineItemInput,
  LineItemOption,
  QuantityBasisEnum,
} from "@/types/calculation";
import {
  CalculationDetails,
  CalculationUpdateData,
} from "@/types/calculations";
import { showError } from "@/lib/notifications";
import { toast } from "sonner";

// Import data service for read operations
import { getCalculationById } from "./calculationDataService";

// ===== HELPER FUNCTIONS =====

/**
 * Helper function to calculate the total price of an item based on quantity_basis
 * @param item - The item to calculate the total price for
 * @returns The calculated total price
 */
const calculateItemTotal = (item: any): number => {
  const unitPrice = item.unit_price || 0;
  const quantity = item.item_quantity || 0;
  const itemQuantityBasis = item.item_quantity_basis || 1;
  const basis = item.quantity_basis || "PER_DAY";

  console.log(`[calculateItemTotal] Calculating for "${item.item_name}":`, {
    unitPrice,
    quantity,
    itemQuantityBasis,
    basis,
  });

  // ALL quantity basis types should multiply by unitPrice * quantity * itemQuantityBasis
  // This ensures consistent calculation logic across all types
  const total = unitPrice * quantity * itemQuantityBasis;

  console.log(
    `[calculateItemTotal] Result: ${unitPrice} × ${quantity} × ${itemQuantityBasis} = ${total}`
  );

  return total;
};

/**
 * Helper function to get a line item by ID
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @returns The line item
 */
const getLineItemById = async (
  calculationId: string,
  lineItemId: string
): Promise<LineItem> => {
  // Get all line items and find the one we need
  const { getCalculationLineItems } = await import(
    "./line-items/lineItemService"
  );
  const lineItems = await getCalculationLineItems(calculationId);
  const lineItem = lineItems.find((item) => item.id === lineItemId);

  if (!lineItem) {
    throw new Error(`Line item ${lineItemId} not found`);
  }

  return lineItem;
};

// ===== LINE ITEM MUTATIONS =====

/**
 * Add a line item to a calculation using Supabase RPC
 * CONSOLIDATED: Direct implementation with both package and custom item support
 * @param calculationId - The calculation ID
 * @param lineItem - The line item data to add
 * @returns The created line item
 */
export const addLineItem = async (
  calculationId: string,
  lineItem: LineItemInput
): Promise<LineItem> => {
  try {
    console.log(
      `[Mutation] Adding line item to calculation ID ${calculationId}:`,
      lineItem
    );

    // Determine if this is a package or custom item
    if (!lineItem.is_custom && lineItem.package_id) {
      // First, fetch the calculation to get the currency ID
      const { data: calculationData, error: calculationError } = await supabase
        .from("calculation_history")
        .select("currency_id")
        .eq("id", calculationId)
        .single();

      if (calculationError) {
        console.error("Error fetching calculation currency:", calculationError);
        throw calculationError;
      }

      if (!calculationData?.currency_id) {
        throw new Error("Calculation currency ID not found");
      }

      // This is a package line item, use the RPC function
      const { data: newLineItemId, error } = await supabase.rpc(
        "add_package_item_and_recalculate",
        {
          p_calculation_id: calculationId,
          p_user_id: (await supabase.auth.getUser()).data.user?.id,
          p_package_id: lineItem.package_id,
          p_option_ids: lineItem.selectedOptions || [],
          p_currency_id: calculationData.currency_id,
          p_quantity_override: lineItem.quantity,
          p_duration_override: lineItem.item_quantity_basis || 1,
          p_notes: lineItem.description || "",
        }
      );

      if (error) {
        console.error("Error adding package line item:", error);
        throw error;
      }

      // Fetch the newly created line item
      const newItem = await getLineItemById(calculationId, newLineItemId);
      return newItem;
    } else {
      // This is a custom line item
      // First, fetch the calculation to get the currency ID
      const { data: calcData, error: calcError } = await supabase
        .from("calculation_history")
        .select("currency_id")
        .eq("id", calculationId)
        .single();

      if (calcError) {
        console.error("Error fetching calculation currency:", calcError);
        throw calcError;
      }

      if (!calcData?.currency_id) {
        throw new Error("Calculation currency ID not found");
      }

      const currencyId = calcData.currency_id;

      const { data, error } = await supabase
        .from("calculation_custom_items")
        .insert({
          calculation_id: calculationId,
          item_name: lineItem.name,
          description: lineItem.description || null,
          item_quantity: lineItem.quantity,
          item_quantity_basis: lineItem.item_quantity_basis || 1,
          quantity_basis: lineItem.quantity_basis || "PER_DAY",
          unit_price: lineItem.unit_price || 0,
          unit_cost: 0, // Default to 0 if not provided
          category_id: lineItem.category_id || null,
          currency_id: currencyId,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding custom line item:", error);
        throw error;
      }

      // Trigger recalculation
      await recalculateCalculationTotals(calculationId);

      // Transform to LineItem format
      const newItem: LineItem = {
        id: data.id,
        calculation_id: data.calculation_id,
        name: data.item_name,
        description: data.description || undefined,
        quantity: data.item_quantity,
        item_quantity_basis: data.item_quantity_basis || 1,
        unit_price: data.unit_price,
        total_price: calculateItemTotal(data),
        category_id: data.category_id || "",
        quantity_basis:
          data.quantity_basis && typeof data.quantity_basis === "string"
            ? QuantityBasisEnum[
                data.quantity_basis as keyof typeof QuantityBasisEnum
              ] || QuantityBasisEnum.PER_DAY
            : QuantityBasisEnum.PER_DAY,
        is_custom: true,
        created_at: data.created_at,
        updated_at: data.updated_at,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };

      return newItem;
    }
  } catch (error) {
    console.error(
      `[Mutation] Error adding line item to calculation ID ${calculationId}:`,
      error
    );
    showError("Failed to add line item", {
      description:
        "There was an error adding the line item to the calculation. Please try again.",
    });
    throw error;
  }
};

/**
 * Remove a line item from a calculation
 * CONSOLIDATED: Direct implementation with both package and custom item support
 * @param calculationId - The calculation ID
 * @param lineItemId - The line item ID
 * @param isCustom - Whether this is a custom item or package item
 */
export const removeLineItem = async (
  calculationId: string,
  lineItemId: string,
  isCustom: boolean = false
): Promise<void> => {
  try {
    console.log(
      `[Mutation] Removing line item ${lineItemId} from calculation ${calculationId}`
    );

    if (isCustom) {
      // Delete custom line item
      const { error } = await supabase
        .from("calculation_custom_items")
        .delete()
        .eq("id", lineItemId)
        .eq("calculation_id", calculationId);

      if (error) {
        console.error("Error removing custom line item:", error);
        throw error;
      }
    } else {
      // Delete package line item
      const { error } = await supabase
        .from("calculation_line_items")
        .delete()
        .eq("id", lineItemId)
        .eq("calculation_id", calculationId);

      if (error) {
        console.error("Error removing package line item:", error);
        throw error;
      }
    }

    // Trigger recalculation
    await recalculateCalculationTotals(calculationId);

    console.log(`[Mutation] Successfully removed line item ${lineItemId}`);
  } catch (error) {
    console.error(`[Mutation] Error removing line item ${lineItemId}:`, error);
    showError("Failed to remove line item", {
      description:
        "There was an error removing the line item. Please try again.",
    });
    throw error;
  }
};

/**
 * Recalculate calculation totals directly with Supabase
 * CONSOLIDATED: Direct implementation instead of delegation
 * @param calculationId - The calculation ID
 */
export const recalculateCalculationTotals = async (
  calculationId: string
): Promise<void> => {
  try {
    console.log(
      `[Mutation] Recalculating totals for calculation ${calculationId} with Supabase`
    );

    // Call the RPC function
    const { error } = await supabase.rpc("recalculate_calculation_totals", {
      p_calculation_id: calculationId,
    });

    if (error) {
      console.error(
        `Error recalculating totals for calculation ${calculationId}:`,
        error
      );
      throw error;
    }

    console.log(
      `[Mutation] Successfully recalculated totals for calculation ${calculationId} with Supabase`
    );
  } catch (error) {
    console.error(
      `[Mutation] Error recalculating totals for calculation ${calculationId} with Supabase:`,
      error
    );
    throw error;
  }
};

// ===== CALCULATION MUTATIONS =====

/**
 * Update an existing calculation using the backend API
 * CONSOLIDATED: Moved from calculationDataService for better organization
 * @param id - The calculation ID
 * @param calculationData - The calculation data to update
 * @returns The updated calculation
 */
export const updateCalculation = async (
  id: string,
  calculationData: CalculationUpdateData
): Promise<CalculationDetails> => {
  try {
    console.log(
      `[Mutation] Updating calculation ${id} using backend API:`,
      calculationData
    );

    // Use the backend API instead of direct Supabase calls
    const authClient = await getAuthenticatedApiClient();

    // Prepare the update payload for the backend API
    const updatePayload: any = {};

    // Only include fields that are provided in the update data
    if (calculationData.name !== undefined)
      updatePayload.name = calculationData.name;
    if (calculationData.event_type_id !== undefined)
      updatePayload.event_type_id = calculationData.event_type_id;
    if (calculationData.attendees !== undefined)
      updatePayload.attendees = calculationData.attendees;
    if (calculationData.notes !== undefined)
      updatePayload.notes = calculationData.notes;
    if (calculationData.event_start_date !== undefined)
      updatePayload.event_start_date = calculationData.event_start_date;
    if (calculationData.event_end_date !== undefined)
      updatePayload.event_end_date = calculationData.event_end_date;
    if (calculationData.status !== undefined)
      updatePayload.status = calculationData.status;
    if (calculationData.taxes !== undefined) {
      // Handle taxes with proper backend structure
      updatePayload.taxes = Array.isArray(calculationData.taxes)
        ? calculationData.taxes.map((tax) => ({
            name: tax.name || "Tax",
            rate: typeof tax.rate === "number" ? tax.rate : 0,
            type: tax.type || "percentage",
            basis: tax.basis || "subtotal",
          }))
        : [];
    }
    if (calculationData.discount !== undefined) {
      // Handle discount with proper backend structure
      if (calculationData.discount && calculationData.discount.value > 0) {
        updatePayload.discount = {
          name: calculationData.discount.name || "Discount",
          // For backend API, use 'amount' instead of 'value' for fixed discounts
          amount: calculationData.discount.value,
        };
      } else {
        updatePayload.discount = null;
      }
    }

    // Update the calculation using the backend API
    const response = await authClient.put(
      API_ENDPOINTS.CALCULATIONS.UPDATE(id),
      updatePayload
    );

    console.log(
      `[Mutation] Successfully updated calculation ${id} using backend API`
    );

    // Return the updated calculation data
    return response.data;
  } catch (error) {
    console.error(
      `[Mutation] Error updating calculation with ID ${id} using Supabase:`,
      error
    );
    toast.error("Failed to update calculation");
    throw error;
  }
};

/**
 * Delete a calculation (soft delete)
 * CONSOLIDATED: Moved from calculationDataService for better organization
 * @param calculationId - The calculation ID
 * @returns True if the calculation was deleted successfully
 */
export const deleteCalculation = async (
  calculationId: string
): Promise<boolean> => {
  try {
    console.log(`[Mutation] Deleting calculation with ID: ${calculationId}`);

    // Get the current user
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      throw new Error(
        "User not authenticated. Please sign in to delete a calculation."
      );
    }

    // Soft delete the calculation
    const { error } = await supabase
      .from("calculation_history")
      .update({
        is_deleted: true,
        deleted_at: new Date().toISOString(),
        status: "canceled",
      })
      .eq("id", calculationId);

    if (error) {
      console.error(
        `[Mutation] Error deleting calculation ${calculationId}:`,
        error
      );
      throw error;
    }

    console.log(`[Mutation] Successfully deleted calculation ${calculationId}`);
    toast.success("Calculation deleted successfully");
    return true;
  } catch (error) {
    console.error(
      `[Mutation] Error deleting calculation ${calculationId}:`,
      error
    );
    toast.error("Failed to delete calculation");
    throw error;
  }
};

{"version": 3, "file": "cache-monitoring.service.js", "sourceRoot": "", "sources": ["../../../src/core/cache/cache-monitoring.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAOA,2CAAoD;AACpD,mDAIyB;AA0BlB,IAAM,sBAAsB,8BAA5B,MAAM,sBAAsB;IAOJ;IANZ,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAsB,CAAC,IAAI,CAAC,CAAC;IAC1D,MAAM,GAAiB,EAAE,CAAC;IAC1B,kBAAkB,GAA6B,EAAE,CAAC;IACzC,cAAc,GAAG,GAAG,CAAC;IACrB,aAAa,GAAG,EAAE,CAAC;IAEpC,YAA6B,YAA0B;QAA1B,iBAAY,GAAZ,YAAY,CAAc;IAAG,CAAC;IAM3D,uBAAuB;QACrB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;YAGnD,MAAM,MAAM,GAA2B;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,MAAM;gBACd,OAAO;gBACP,MAAM;gBACN,MAAM,EAAE,EAAE;gBACV,eAAe,EAAE,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,MAAM,CAAC;aAC/D,CAAC;YAGF,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;YAG1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAC/B,IAAI,CAAC,UAAU,EAAE,CAAC;YAGlB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,WAAW,EAAE,CAAC;YAGnB,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iCAAiC,MAAM,CAAC,OAAO,kBAAkB,MAAM,CAAC,SAAS,cAAc,MAAM,CAAC,MAAM,EAAE,CAC/G,CAAC;YAGF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;YAGpE,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE,CAAC;gBAC3D,IAAI,CAAC,oBAAoB,CACvB,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,CAAC,CACzD,CAAC;YACJ,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAKD,uBAAuB,CAAC,KAA6B;QACnD,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,gCAAgC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,MAAM,GAAG,CAC7E,CAAC;IAIJ,CAAC;IAKO,cAAc,CACpB,OAAqB,EACrB,MAA8D;QAE9D,MAAM,MAAM,GAAiB,EAAE,CAAC;QAChC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QAGvB,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,YAAY,GAAG,CAAC,OAAO,EAAE,EAAE;gBAC/B,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,kCAAkC,MAAM,CAAC,OAAO,GAAG;gBAC5D,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;aACrC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,YAAY,GAAG,CAAC,OAAO,EAAE,EAAE;gBAC/B,IAAI,EAAE,aAAa;gBACnB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,iCAAiC,MAAM,CAAC,OAAO,GAAG;gBAC3D,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE;aACrC,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,MAAM,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,cAAc,GAAG,CAAC,OAAO,EAAE,EAAE;gBACjC,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,UAAU;gBACpB,OAAO,EAAE,qCAAqC,MAAM,CAAC,SAAS,GAAG;gBACjE,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;aACzC,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,cAAc,GAAG,CAAC,OAAO,EAAE,EAAE;gBACjC,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,8BAA8B,MAAM,CAAC,SAAS,GAAG;gBAC1D,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE;aACzC,CAAC,CAAC;QACL,CAAC;QAGD,MAAM,QAAQ,GACZ,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;QACjE,IAAI,QAAQ,GAAG,KAAK,EAAE,CAAC;YAErB,MAAM,CAAC,IAAI,CAAC;gBACV,EAAE,EAAE,iBAAiB,GAAG,CAAC,OAAO,EAAE,EAAE;gBACpC,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE,QAAQ;gBAClB,OAAO,EAAE,iCAAiC,QAAQ,aAAa;gBAC/D,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,EAAE,eAAe,EAAE,QAAQ,EAAE;aACvC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAKO,uBAAuB,CAC7B,OAAqB,EACrB,MAA8D;QAE9D,MAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YACxB,eAAe,CAAC,IAAI,CAClB,sDAAsD,CACvD,CAAC;YACF,eAAe,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;QAC3E,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACzB,eAAe,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YAC5D,eAAe,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;QAC/C,IAAI,QAAQ,GAAG,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,QAAQ,GAAG,GAAG,EAAE,CAAC;YACpD,eAAe,CAAC,IAAI,CAClB,0DAA0D,CAC3D,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;YACzC,eAAe,CAAC,IAAI,CAClB,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QAED,OAAO,eAAe,CAAC;IACzB,CAAC;IAKO,oBAAoB,CAAC,cAA4B;QACvD,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAG5D,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAI/D,CAAC;IACH,CAAC;IAKD,cAAc;QAWZ,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAElE,MAAM,qBAAqB,GAAG,IAAI,CAAC,kBAAkB;aAClD,KAAK,CAAC,CAAC,CAAC,CAAC;aACT,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC;aACzC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC;QAE3D,OAAO;YACL,MAAM;YACN,OAAO;YACP,YAAY;YACZ,qBAAqB;SACtB,CAAC;IACJ,CAAC;IAKD,qBAAqB,CAAC,QAAgB,EAAE;QACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC/C,CAAC;IAKD,gBAAgB,CAAC,QAAgB,EAAE;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAKD,YAAY,CAAC,OAAe;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACtD,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,mBAAmB,OAAO,EAAE,CAAC,CAAC;YAC9C,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAKD,gBAAgB;QAoBd,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAC1D,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;QAG/C,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACzD,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC9D,MAAM,cAAc,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAClE,MAAM,eAAe,GAAG,aAAa,CAAC,GAAG,CACvC,CAAC,CAAC,EAAE,CACF,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CACzE,CAAC;QAGF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QAC1D,MAAM,WAAW,GAAG;YAClB,QAAQ,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,MAAM;YACpE,IAAI,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;YAC5D,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;YAChE,GAAG,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;SAC3D,CAAC;QAEF,OAAO;YACL,aAAa;YACb,OAAO;YACP,MAAM,EAAE;gBACN,YAAY;gBACZ,cAAc;gBACd,eAAe;aAChB;YACD,MAAM,EAAE,WAAW;SACpB,CAAC;IACJ,CAAC;IAKO,UAAU;QAChB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAKO,WAAW;QACjB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACzD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CACrD,CAAC,IAAI,CAAC,cAAc,CACrB,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,mBAAmB;QACjB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IACjD,CAAC;CACF,CAAA;AAhVY,wDAAsB;iCAAtB,sBAAsB;IADlC,IAAA,mBAAU,GAAE;qCAQgC,4BAAY;GAP5C,sBAAsB,CAgVlC"}
/**
 * Validation Cache System
 * 
 * Phase 4 Performance Optimization: LRU cache for UUID validation results
 * to prevent redundant validation operations and improve performance.
 */

import { isValidUUID } from './uuidValidation';

/**
 * Cache entry interface
 */
interface CacheEntry {
  /** The validation result */
  isValid: boolean;
  
  /** Timestamp when the entry was created */
  timestamp: number;
  
  /** Number of times this entry has been accessed */
  accessCount: number;
  
  /** Last access timestamp */
  lastAccessed: number;
}

/**
 * Cache statistics interface
 */
export interface CacheStatistics {
  /** Total number of cache hits */
  hits: number;
  
  /** Total number of cache misses */
  misses: number;
  
  /** Cache hit rate as percentage */
  hitRate: number;
  
  /** Current cache size */
  size: number;
  
  /** Maximum cache size */
  maxSize: number;
  
  /** Total validations performed */
  totalValidations: number;
  
  /** Average validation time (ms) */
  averageValidationTime: number;
  
  /** Cache memory usage estimate (bytes) */
  memoryUsage: number;
}

/**
 * Cache configuration interface
 */
export interface CacheConfig {
  /** Maximum number of entries in cache */
  maxSize?: number;
  
  /** Time-to-live for cache entries (ms) */
  ttl?: number;
  
  /** Whether to enable performance tracking */
  enablePerformanceTracking?: boolean;
  
  /** Whether to log cache operations in development */
  enableLogging?: boolean;
}

/**
 * LRU Validation Cache Class
 * 
 * Implements a Least Recently Used cache for UUID validation results
 * with performance tracking and automatic cleanup.
 */
class ValidationCache {
  private cache = new Map<string, CacheEntry>();
  private maxSize: number;
  private ttl: number;
  private enablePerformanceTracking: boolean;
  private enableLogging: boolean;
  
  // Performance tracking
  private hits = 0;
  private misses = 0;
  private validationTimes: number[] = [];
  
  constructor(config: CacheConfig = {}) {
    this.maxSize = config.maxSize ?? 100;
    this.ttl = config.ttl ?? 5 * 60 * 1000; // 5 minutes default
    this.enablePerformanceTracking = config.enablePerformanceTracking ?? true;
    this.enableLogging = config.enableLogging ?? (process.env.NODE_ENV === 'development');
    
    // Periodic cleanup of expired entries
    if (typeof window !== 'undefined') {
      setInterval(() => this.cleanup(), 60000); // Cleanup every minute
    }
  }

  /**
   * Validate a UUID with caching
   * 
   * @param id - The UUID string to validate
   * @returns Validation result (true if valid UUID)
   */
  validate(id: string | undefined): boolean {
    if (!id || typeof id !== 'string') {
      this.log('Validation cache: Invalid input (undefined or non-string)');
      return false;
    }

    const trimmedId = id.trim();
    const startTime = this.enablePerformanceTracking ? performance.now() : 0;

    // Check cache first
    const cached = this.get(trimmedId);
    if (cached !== null) {
      this.hits++;
      this.log(`Validation cache HIT for: ${trimmedId.substring(0, 8)}...`);
      
      if (this.enablePerformanceTracking) {
        const endTime = performance.now();
        this.validationTimes.push(endTime - startTime);
      }
      
      return cached;
    }

    // Cache miss - perform validation
    this.misses++;
    this.log(`Validation cache MISS for: ${trimmedId.substring(0, 8)}...`);
    
    const isValid = isValidUUID(trimmedId);
    this.set(trimmedId, isValid);
    
    if (this.enablePerformanceTracking) {
      const endTime = performance.now();
      this.validationTimes.push(endTime - startTime);
    }
    
    return isValid;
  }

  /**
   * Get a value from cache
   * 
   * @param key - Cache key
   * @returns Cached validation result or null if not found/expired
   */
  private get(key: string): boolean | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if entry has expired
    if (Date.now() - entry.timestamp > this.ttl) {
      this.cache.delete(key);
      this.log(`Validation cache: Expired entry removed for ${key.substring(0, 8)}...`);
      return null;
    }
    
    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    // Move to end (most recently used) in Map
    this.cache.delete(key);
    this.cache.set(key, entry);
    
    return entry.isValid;
  }

  /**
   * Set a value in cache
   * 
   * @param key - Cache key
   * @param isValid - Validation result
   */
  private set(key: string, isValid: boolean): void {
    // Remove oldest entry if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
      this.log(`Validation cache: LRU eviction of ${oldestKey?.substring(0, 8)}...`);
    }
    
    const entry: CacheEntry = {
      isValid,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
    };
    
    this.cache.set(key, entry);
    this.log(`Validation cache: Cached result for ${key.substring(0, 8)}... = ${isValid}`);
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      this.log(`Validation cache: Cleaned up ${removedCount} expired entries`);
    }
  }

  /**
   * Get cache statistics
   * 
   * @returns Current cache statistics
   */
  getStatistics(): CacheStatistics {
    const totalValidations = this.hits + this.misses;
    const hitRate = totalValidations > 0 ? (this.hits / totalValidations) * 100 : 0;
    const averageValidationTime = this.validationTimes.length > 0
      ? this.validationTimes.reduce((sum, time) => sum + time, 0) / this.validationTimes.length
      : 0;
    
    // Estimate memory usage (rough calculation)
    const memoryUsage = this.cache.size * (
      36 + // UUID string (36 chars)
      8 + // boolean
      8 + // timestamp
      4 + // accessCount
      8   // lastAccessed
    );
    
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      size: this.cache.size,
      maxSize: this.maxSize,
      totalValidations,
      averageValidationTime: Math.round(averageValidationTime * 100) / 100,
      memoryUsage,
    };
  }

  /**
   * Clear the cache
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
    this.validationTimes = [];
    this.log(`Validation cache: Cleared ${size} entries`);
  }

  /**
   * Get cache entries for debugging
   * 
   * @returns Array of cache entries with keys
   */
  getEntries(): Array<{ key: string; entry: CacheEntry }> {
    return Array.from(this.cache.entries()).map(([key, entry]) => ({
      key: key.substring(0, 8) + '...', // Truncate for privacy
      entry: { ...entry },
    }));
  }

  /**
   * Preload validation results for known UUIDs
   * 
   * @param uuids - Array of UUIDs to preload
   */
  preload(uuids: string[]): void {
    uuids.forEach(uuid => {
      if (uuid && !this.cache.has(uuid.trim())) {
        this.validate(uuid);
      }
    });
    
    this.log(`Validation cache: Preloaded ${uuids.length} UUIDs`);
  }

  /**
   * Log cache operations (development only)
   * 
   * @param message - Log message
   */
  private log(message: string): void {
    if (this.enableLogging) {
      console.log(`[ValidationCache] ${message}`);
    }
  }
}

// Global cache instance
export const validationCache = new ValidationCache({
  maxSize: 100,
  ttl: 5 * 60 * 1000, // 5 minutes
  enablePerformanceTracking: true,
  enableLogging: process.env.NODE_ENV === 'development',
});

/**
 * Hook for accessing validation cache statistics
 */
export const useValidationCacheStats = () => {
  return {
    getStatistics: () => validationCache.getStatistics(),
    clear: () => validationCache.clear(),
    getEntries: () => validationCache.getEntries(),
    preload: (uuids: string[]) => validationCache.preload(uuids),
  };
};

export default validationCache;

import { CacheService } from './cache.service';
import { CacheWarmingService } from './cache-warming.service';
import { CacheMonitoringService } from './cache-monitoring.service';
export declare class CacheOperationDto {
    keys?: string[];
    pattern?: string;
    force?: boolean;
}
export declare class CacheWarmingDto {
    keys?: string[];
    priority?: number;
}
export declare class CacheController {
    private readonly cacheService;
    private readonly cacheWarmingService;
    private readonly cacheMonitoringService;
    constructor(cacheService: CacheService, cacheWarmingService: CacheWarmingService, cacheMonitoringService: CacheMonitoringService);
    getCacheStatus(): {
        status: "healthy" | "degraded" | "unhealthy";
        uptime: string;
        totalEntries: number;
        memoryUsage: {
            used: string;
            percentage: number;
        };
        hitRate: number;
        errorRate: number;
        lastWarming: Date | null;
        activeKeys: string[];
    };
    getCacheHealth(): {
        health: {
            status: string;
            hitRate: number;
            errorRate: number;
            metrics: import("./cache.service").CacheMetrics;
        };
        metrics: import("./cache.service").CacheMetrics;
        activeAlerts: import("./cache-monitoring.service").CacheAlert[];
        recentRecommendations: string[];
    };
    getCacheDashboard(): {
        currentHealth: {
            status: string;
            hitRate: number;
            errorRate: number;
            metrics: import("./cache.service").CacheMetrics;
        };
        metrics: import("./cache.service").CacheMetrics;
        trends: {
            hitRateTrend: number[];
            errorRateTrend: number[];
            operationsTrend: number[];
        };
        alerts: {
            critical: number;
            high: number;
            medium: number;
            low: number;
        };
    };
    getCacheMetrics(): import("./cache.service").CacheMetrics;
    getPerformanceHistory(limit?: string): import("./cache-monitoring.service").CachePerformanceReport[];
    getCacheAlerts(limit?: string): import("./cache-monitoring.service").CacheAlert[];
    resolveAlert(alertId: string): {
        success: boolean;
        alertId: string;
    };
    warmCache(dto: CacheWarmingDto): Promise<{
        success: boolean;
        message: string;
    }>;
    getCacheWarmingStatus(): {
        totalConfigs: number;
        enabledConfigs: number;
        lastWarming: Date | null;
        nextScheduledWarming: Date | null;
    };
    toggleCacheWarming(key: string, enabled: boolean): {
        success: boolean;
        key: string;
        enabled: boolean;
    };
    clearCacheKeys(dto: CacheOperationDto): Promise<{
        success: boolean;
        clearedKeys: string[];
        pattern?: undefined;
        message?: undefined;
    } | {
        success: boolean;
        pattern: string;
        clearedKeys?: undefined;
        message?: undefined;
    } | {
        success: boolean;
        message: string;
        clearedKeys?: undefined;
        pattern?: undefined;
    }>;
    clearAllCache(dto: CacheOperationDto): Promise<{
        success: boolean;
        message: string;
    }>;
    resetCacheMetrics(): {
        success: boolean;
        message: string;
    };
    resetMonitoringData(): {
        success: boolean;
        message: string;
    };
    triggerPerformanceCheck(): {
        success: boolean;
        message: string;
    };
    getCacheKeyInfo(key: string): Promise<{
        key: string;
        exists: boolean;
        value: {} | null;
        type: "string" | "number" | "bigint" | "boolean" | "symbol" | "undefined" | "object" | "function";
    }>;
    setCacheKey(key: string, value: unknown, ttl?: number): Promise<{
        success: boolean;
        key: string;
        value: unknown;
        ttl: number | undefined;
    }>;
    testCachePerformance(iterations?: number): Promise<{
        iterations: number;
        performance: {
            totalTime: string;
            writeTime: string;
            readTime: string;
            avgWriteTime: string;
            avgReadTime: string;
        };
    }>;
    getCacheStats(): {
        metrics: import("./cache.service").CacheMetrics;
        health: {
            status: "healthy" | "degraded" | "unhealthy";
            hitRate: number;
            errorRate: number;
            metrics: import("./cache.service").CacheMetrics;
        };
        warming: {
            totalConfigs: number;
            enabledConfigs: number;
            lastWarming: Date | null;
            nextScheduledWarming: Date | null;
        };
        uptime: number;
        timestamp: Date;
    };
    exportCacheData(): {
        exportTimestamp: Date;
        version: string;
        metrics: import("./cache.service").CacheMetrics;
        health: {
            status: "healthy" | "degraded" | "unhealthy";
            hitRate: number;
            errorRate: number;
            metrics: import("./cache.service").CacheMetrics;
        };
        dashboard: {
            currentHealth: {
                status: string;
                hitRate: number;
                errorRate: number;
                metrics: import("./cache.service").CacheMetrics;
            };
            metrics: import("./cache.service").CacheMetrics;
            trends: {
                hitRateTrend: number[];
                errorRateTrend: number[];
                operationsTrend: number[];
            };
            alerts: {
                critical: number;
                high: number;
                medium: number;
                low: number;
            };
        };
        warming: {
            totalConfigs: number;
            enabledConfigs: number;
            lastWarming: Date | null;
            nextScheduledWarming: Date | null;
        };
        environment: string | undefined;
    };
}

/**
 * Validation Context Hooks
 *
 * Separated from ValidationContext.tsx to satisfy React Fast Refresh requirements.
 * Fast Refresh only works when a file only exports components OR only exports hooks.
 */

import { useContext } from "react";
import ValidationContext, { ValidationContextType } from "./ValidationContext";

/**
 * Hook to consume validation context
 *
 * Provides access to validation state and functions.
 * Must be used within a ValidationProvider.
 *
 * @returns Validation context with isValid state and validation functions
 * @throws Error if used outside ValidationProvider
 */
export const useValidation = () => {
  const context = useContext(ValidationContext);

  if (context === null) {
    throw new Error(
      "useValidation must be used within a ValidationProvider. " +
        "Make sure to wrap your component with <ValidationProvider>."
    );
  }

  return context;
};

/**
 * Type guard to check if validation context is available
 *
 * @param context - The validation context to check
 * @returns True if context is available and valid
 */
export const isValidationContextAvailable = (
  context: ValidationContextType | null
): context is ValidationContextType => {
  return context !== null;
};

/**
 * Hook to safely consume validation context with fallback
 *
 * Returns null if context is not available instead of throwing an error.
 * Useful for components that might be used outside ValidationProvider.
 *
 * @returns Validation context or null if not available
 */
export const useValidationSafe = (): ValidationContextType | null => {
  return useContext(ValidationContext);
};

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheController = exports.CacheWarmingDto = exports.CacheOperationDto = void 0;
const common_1 = require("@nestjs/common");
const cache_service_1 = require("./cache.service");
const cache_warming_service_1 = require("./cache-warming.service");
const cache_monitoring_service_1 = require("./cache-monitoring.service");
class CacheOperationDto {
    keys;
    pattern;
    force;
}
exports.CacheOperationDto = CacheOperationDto;
class CacheWarmingDto {
    keys;
    priority;
}
exports.CacheWarmingDto = CacheWarmingDto;
let CacheController = class CacheController {
    cacheService;
    cacheWarmingService;
    cacheMonitoringService;
    constructor(cacheService, cacheWarmingService, cacheMonitoringService) {
        this.cacheService = cacheService;
        this.cacheWarmingService = cacheWarmingService;
        this.cacheMonitoringService = cacheMonitoringService;
    }
    getCacheStatus() {
        const health = this.cacheService.getHealthStatus();
        const warmingStatus = this.cacheWarmingService.getWarmingStatus();
        return {
            status: health.status,
            uptime: `${Math.floor(process.uptime() / 3600)}h ${Math.floor((process.uptime() % 3600) / 60)}m ${Math.floor(process.uptime() % 60)}s`,
            totalEntries: this.cacheService.getCacheSize(),
            memoryUsage: {
                used: `${(process.memoryUsage().heapUsed / 1024 / 1024).toFixed(1)} MB`,
                percentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) *
                    100),
            },
            hitRate: health.hitRate,
            errorRate: health.errorRate,
            lastWarming: warmingStatus.lastWarming,
            activeKeys: this.cacheService.getAllKeys(),
        };
    }
    getCacheHealth() {
        return this.cacheMonitoringService.getCacheStatus();
    }
    getCacheDashboard() {
        return this.cacheMonitoringService.getDashboardData();
    }
    getCacheMetrics() {
        return this.cacheService.getMetrics();
    }
    getPerformanceHistory(limit) {
        const limitNum = limit ? parseInt(limit, 10) : 20;
        return this.cacheMonitoringService.getPerformanceHistory(limitNum);
    }
    getCacheAlerts(limit) {
        const limitNum = limit ? parseInt(limit, 10) : 20;
        return this.cacheMonitoringService.getAlertsHistory(limitNum);
    }
    resolveAlert(alertId) {
        const resolved = this.cacheMonitoringService.resolveAlert(alertId);
        return { success: resolved, alertId };
    }
    async warmCache(dto) {
        await this.cacheWarmingService.warmCacheManually(dto.keys);
        return { success: true, message: 'Cache warming initiated' };
    }
    getCacheWarmingStatus() {
        return this.cacheWarmingService.getWarmingStatus();
    }
    toggleCacheWarming(key, enabled) {
        const success = this.cacheWarmingService.toggleCacheWarming(key, enabled);
        return { success, key, enabled };
    }
    async clearCacheKeys(dto) {
        if (dto.keys) {
            for (const key of dto.keys) {
                await this.cacheService.delete(key);
            }
            return { success: true, clearedKeys: dto.keys };
        }
        if (dto.pattern) {
            await this.cacheService.deleteByPattern(dto.pattern);
            return { success: true, pattern: dto.pattern };
        }
        return { success: false, message: 'No keys or pattern specified' };
    }
    async clearAllCache(dto) {
        if (dto.force) {
            await this.cacheService.clear();
            return { success: true, message: 'All cache cleared' };
        }
        return {
            success: false,
            message: 'Force flag required to clear all cache',
        };
    }
    resetCacheMetrics() {
        this.cacheService.resetMetrics();
        return { success: true, message: 'Cache metrics reset' };
    }
    resetMonitoringData() {
        this.cacheMonitoringService.resetMonitoringData();
        return { success: true, message: 'Monitoring data reset' };
    }
    triggerPerformanceCheck() {
        this.cacheMonitoringService.monitorCachePerformance();
        return { success: true, message: 'Performance check completed' };
    }
    async getCacheKeyInfo(key) {
        const value = await this.cacheService.get(key);
        return {
            key,
            exists: value !== null && value !== undefined,
            value: value
                ? typeof value === 'object'
                    ? JSON.stringify(value)
                    : value
                : null,
            type: typeof value,
        };
    }
    async setCacheKey(key, value, ttl) {
        await this.cacheService.set(key, value, ttl);
        return { success: true, key, value, ttl };
    }
    async testCachePerformance(iterations) {
        const iterationsCount = iterations || 100;
        const testKey = `test:performance:${Date.now()}`;
        const testValue = { data: 'test', timestamp: Date.now() };
        const startTime = Date.now();
        const writeStart = Date.now();
        for (let i = 0; i < iterationsCount; i++) {
            await this.cacheService.set(`${testKey}:${i}`, testValue, 60);
        }
        const writeTime = Date.now() - writeStart;
        const readStart = Date.now();
        for (let i = 0; i < iterationsCount; i++) {
            await this.cacheService.get(`${testKey}:${i}`);
        }
        const readTime = Date.now() - readStart;
        for (let i = 0; i < iterationsCount; i++) {
            await this.cacheService.delete(`${testKey}:${i}`);
        }
        const totalTime = Date.now() - startTime;
        return {
            iterations: iterationsCount,
            performance: {
                totalTime: `${totalTime}ms`,
                writeTime: `${writeTime}ms`,
                readTime: `${readTime}ms`,
                avgWriteTime: `${(writeTime / iterationsCount).toFixed(2)}ms`,
                avgReadTime: `${(readTime / iterationsCount).toFixed(2)}ms`,
            },
        };
    }
    getCacheStats() {
        const metrics = this.cacheService.getMetrics();
        const health = this.cacheService.getHealthStatus();
        const warmingStatus = this.cacheWarmingService.getWarmingStatus();
        return {
            metrics,
            health,
            warming: warmingStatus,
            uptime: process.uptime(),
            timestamp: new Date(),
        };
    }
    exportCacheData() {
        const metrics = this.cacheService.getMetrics();
        const health = this.cacheService.getHealthStatus();
        const dashboard = this.cacheMonitoringService.getDashboardData();
        const warmingStatus = this.cacheWarmingService.getWarmingStatus();
        return {
            exportTimestamp: new Date(),
            version: '2.0.0',
            metrics,
            health,
            dashboard,
            warming: warmingStatus,
            environment: process.env.NODE_ENV,
        };
    }
};
exports.CacheController = CacheController;
__decorate([
    (0, common_1.Get)('status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheStatus", null);
__decorate([
    (0, common_1.Get)('health'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheHealth", null);
__decorate([
    (0, common_1.Get)('dashboard'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheDashboard", null);
__decorate([
    (0, common_1.Get)('metrics'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheMetrics", null);
__decorate([
    (0, common_1.Get)('performance/history'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getPerformanceHistory", null);
__decorate([
    (0, common_1.Get)('alerts'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheAlerts", null);
__decorate([
    (0, common_1.Post)('alerts/:alertId/resolve'),
    __param(0, (0, common_1.Param)('alertId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "resolveAlert", null);
__decorate([
    (0, common_1.Post)('warm'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CacheWarmingDto]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "warmCache", null);
__decorate([
    (0, common_1.Get)('warming/status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheWarmingStatus", null);
__decorate([
    (0, common_1.Post)('warming/:key/toggle'),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)('enabled')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Boolean]),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "toggleCacheWarming", null);
__decorate([
    (0, common_1.Delete)('keys'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CacheOperationDto]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "clearCacheKeys", null);
__decorate([
    (0, common_1.Delete)('all'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [CacheOperationDto]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "clearAllCache", null);
__decorate([
    (0, common_1.Post)('metrics/reset'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "resetCacheMetrics", null);
__decorate([
    (0, common_1.Post)('monitoring/reset'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "resetMonitoringData", null);
__decorate([
    (0, common_1.Post)('monitoring/check'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "triggerPerformanceCheck", null);
__decorate([
    (0, common_1.Get)('keys/:key'),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "getCacheKeyInfo", null);
__decorate([
    (0, common_1.Post)('keys/:key'),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)('value')),
    __param(2, (0, common_1.Body)('ttl')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Number]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "setCacheKey", null);
__decorate([
    (0, common_1.Post)('test/performance'),
    __param(0, (0, common_1.Body)('iterations')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CacheController.prototype, "testCachePerformance", null);
__decorate([
    (0, common_1.Get)('stats'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "getCacheStats", null);
__decorate([
    (0, common_1.Get)('export'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CacheController.prototype, "exportCacheData", null);
exports.CacheController = CacheController = __decorate([
    (0, common_1.Controller)('cache'),
    __metadata("design:paramtypes", [cache_service_1.CacheService,
        cache_warming_service_1.CacheWarmingService,
        cache_monitoring_service_1.CacheMonitoringService])
], CacheController);
//# sourceMappingURL=cache.controller.js.map
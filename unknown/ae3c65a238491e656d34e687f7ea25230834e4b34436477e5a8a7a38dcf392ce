import { Module, Global } from '@nestjs/common';
import {
  CacheModule as NestCacheModule,
  CacheModuleOptions,
} from '@nestjs/cache-manager';
import { ConfigModule, ConfigService } from '@nestjs/config';
import * as redisStore from 'cache-manager-redis-store';
import { CacheService } from './cache.service';
import { CacheWarmingService } from './cache-warming.service';
import { CacheMonitoringService } from './cache-monitoring.service';
import { CacheController } from './cache.controller';

@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService): CacheModuleOptions => {
        const isRedisEnabled =
          configService.get<string>('REDIS_HOST') !== undefined;

        if (isRedisEnabled) {
          return {
            store: redisStore,
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            username: configService.get<string>('REDIS_USERNAME'),
            password: configService.get<string>('REDIS_PASSWORD'),
            ttl: 60 * 60 * 24, // 24 hours default TTL (optimized from previous)
            max: 2000, // Increased cache size for better performance
          };
        } else {
          // Fallback to in-memory cache if Redis is not configured
          return {
            store: 'memory',
            ttl: 60 * 60 * 2, // 2 hours default TTL (increased for better performance)
            max: 1000, // Increased cache size for in-memory
          };
        }
      },
    }),
  ],
  controllers: [CacheController],
  providers: [CacheService, CacheWarmingService, CacheMonitoringService],
  exports: [
    NestCacheModule,
    CacheService,
    CacheWarmingService,
    CacheMonitoringService,
  ],
})
export class CacheModule {}

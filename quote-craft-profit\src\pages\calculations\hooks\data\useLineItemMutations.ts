import { useMutation, useQueryClient } from "@tanstack/react-query";
import { showError } from "@/lib/notifications";
import { optimizedNotifications } from "@/lib/optimized-notifications";
import { v4 as uuidv4 } from "uuid";
import {
  LineItem,
  LineItemInput,
  QuantityBasisEnum,
} from "@/types/calculation";
import { addLineItemWithSupabase } from "../../../../services/calculations";
import { supabase } from "@/integrations/supabase/client";
import { QUERY_KEYS } from "@/lib/queryKeys";
import { useMutationQueue } from "./useMutationQueue";
import { useDebouncedRecalculation } from "./useDebouncedRecalculation";

/**
 * Custom hook for line item mutations with optimistic updates and race condition prevention
 * Now uses mutation queue to ensure sequential processing and prevent data inconsistency
 *
 * @param calculationId - The ID of the calculation
 * @returns Object containing mutation functions for line items
 */
export function useLineItemMutations(calculationId: string) {
  const queryClient = useQueryClient();
  const mutationQueue = useMutationQueue();
  const { debouncedRecalculate, recalculateNow } = useDebouncedRecalculation(
    calculationId,
    {
      delay: 1500, // 1.5 seconds delay for batching
      maxWait: 5000, // Maximum 5 seconds wait
      enabled: true,
    }
  );

  // Helper function for preparing optimistic updates
  const prepareOptimisticUpdate = async () => {
    // Cancel any outgoing refetches using new hierarchical query keys
    await queryClient.cancelQueries({
      queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
    });

    // Get current line items
    return queryClient.getQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId)
    );
  };

  // Helper function for creating an optimistic line item
  const createOptimisticLineItem = async (
    newLineItem: LineItemInput
  ): Promise<LineItem> => {
    // Handle property naming conventions
    const quantity = newLineItem.quantity || 1;
    const unitPrice = newLineItem.unit_price || 0;
    const itemQuantityBasis = newLineItem.item_quantity_basis || 1;
    const totalPrice =
      newLineItem.total_price || quantity * unitPrice * itemQuantityBasis;
    const packageId = newLineItem.package_id || null;
    const isCustom = newLineItem.is_custom || false;
    const quantityBasis =
      newLineItem.quantity_basis ||
      newLineItem.quantityBasis ||
      QuantityBasisEnum.PER_DAY;

    // Get category information if this is a package-based line item
    let categoryId = newLineItem.category_id || "";
    let categoryName = "Loading...";

    if (packageId) {
      try {
        // Try to get the package's category from the cache first using new query keys
        const cachedPackages = queryClient.getQueryData<any[]>(
          QUERY_KEYS.packages.all()
        );
        let packageData = null;

        if (cachedPackages) {
          packageData = cachedPackages.find((pkg) => pkg.id === packageId);
        }

        if (packageData && packageData.category_id) {
          categoryId = packageData.category_id;

          // Try to get the category name from the cache using new query keys
          const cachedCategories = queryClient.getQueryData<any[]>(
            QUERY_KEYS.categories.all()
          );
          let categoryData = null;

          if (cachedCategories) {
            categoryData = cachedCategories.find(
              (cat) => cat.id === categoryId
            );
          }

          if (categoryData) {
            categoryName = categoryData.name;
          }
        }
      } catch (error) {
        console.error(
          "Error getting category information for optimistic update:",
          error
        );
      }
    }

    return {
      id: `temp-${uuidv4()}`, // Temporary ID that will be replaced with the real one
      calculation_id: calculationId,
      name: newLineItem.name || "New Item",
      description: newLineItem.description || "",
      quantity: quantity,
      item_quantity_basis: itemQuantityBasis,
      unit_price: unitPrice,
      total_price: totalPrice,
      category_id: categoryId,
      package_id: packageId,
      is_package: !!packageId,
      is_custom: isCustom,
      quantity_basis: quantityBasis,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      options: [],
      category: {
        id: categoryId,
        name: categoryName,
      },
      _isOptimistic: true, // Flag to indicate this is an optimistic update
    };
  };

  // Helper function for handling post-mutation tasks with debounced recalculation
  const handlePostMutation = async (immediate = false) => {
    try {
      if (immediate) {
        // For critical operations that need immediate recalculation
        console.log("🚀 Immediate recalculation requested");
        await recalculateNow();
      } else {
        // Use debounced recalculation for better performance
        console.log("⏰ Debounced recalculation scheduled");
        debouncedRecalculate();
      }
    } catch (error) {
      console.error("Error in post-mutation tasks:", error);
    }
  };

  /**
   * Add line item with queue management and optimistic updates
   */
  const addLineItem = async (lineItem: LineItemInput) => {
    const previousLineItems = await prepareOptimisticUpdate();
    const optimisticLineItem = await createOptimisticLineItem(lineItem);

    // Optimistically update the UI immediately using new hierarchical query keys
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      previousLineItems
        ? [...previousLineItems, optimisticLineItem]
        : [optimisticLineItem]
    );

    // Add to mutation queue with high priority
    return mutationQueue.enqueue({
      type: "add",
      priority: 10, // High priority for add operations
      operation: async () => {
        const result = await addLineItemWithSupabase(calculationId, lineItem);
        await handlePostMutation(false); // Use debounced recalculation for add operations
        return result;
      },
      onSuccess: (newLineItem) => {
        optimizedNotifications.lineItem.success(
          `Added ${newLineItem.name || "new item"} successfully`
        );
        // Refresh data to get the real item using new hierarchical query keys
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
        });
      },
      onError: (error) => {
        console.error("Error adding line item:", error);
        showError("Failed to add item. Please try again.");

        // Rollback optimistic update using new hierarchical query keys
        if (previousLineItems) {
          queryClient.setQueryData(
            QUERY_KEYS.calculations.lineItems(calculationId),
            previousLineItems
          );
        }
      },
    });
  };

  /**
   * Update line item with queue management and optimistic updates
   */
  const updateLineItem = async (
    lineItemId: string,
    updates: Partial<LineItemInput>
  ) => {
    const previousLineItems = await prepareOptimisticUpdate();

    // Optimistically update the UI immediately using new hierarchical query keys
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      (old) => {
        if (!old) return old;

        return old.map((item) => {
          if (item.id === lineItemId) {
            const quantity =
              updates.quantity !== undefined ? updates.quantity : item.quantity;
            const unitPrice =
              updates.unit_price !== undefined
                ? updates.unit_price
                : item.unit_price || 0;
            const itemQuantityBasis =
              updates.item_quantity_basis !== undefined
                ? updates.item_quantity_basis
                : item.item_quantity_basis || 1;

            // Calculate total price including options (if any)
            // For package items, include options_total_adjustment
            const optionsAdjustment = item.is_custom
              ? 0
              : item.options_total_adjustment || 0;
            const totalPrice =
              (unitPrice + optionsAdjustment) * quantity * itemQuantityBasis;

            return {
              ...item,
              ...updates,
              total_price: totalPrice,
              updated_at: new Date().toISOString(),
              _isOptimistic: true,
            };
          }
          return item;
        });
      }
    );

    // Add to mutation queue with medium priority
    return mutationQueue.enqueue({
      type: "update",
      priority: 8, // Medium priority for update operations
      operation: async () => {
        const { data, error } = await supabase
          .from("calculation_line_items")
          .update({
            item_name_snapshot: updates.name,
            notes: updates.description || "",
            item_quantity: updates.quantity,
            item_quantity_basis: updates.item_quantity_basis,
            unit_base_price: updates.unit_price || 0,
            calculated_line_total: updates.total_price || 0,
            updated_at: new Date().toISOString(),
          })
          .eq("id", lineItemId)
          .eq("calculation_id", calculationId)
          .select("*")
          .single();

        if (error) throw error;

        await handlePostMutation(false); // Use debounced recalculation for update operations
        return data;
      },
      onSuccess: (updatedData) => {
        optimizedNotifications.lineItem.success("Updated item successfully");
        // Refresh data to get the real updated item using new hierarchical query keys
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
        });
      },
      onError: (error) => {
        console.error("Error updating line item:", error);
        showError("Failed to update item. Please try again.");

        // Rollback optimistic update using new hierarchical query keys
        if (previousLineItems) {
          queryClient.setQueryData(
            QUERY_KEYS.calculations.lineItems(calculationId),
            previousLineItems
          );
        }
      },
    });
  };

  /**
   * Remove line item with queue management and optimistic updates
   */
  const removeLineItem = async (lineItemId: string) => {
    const previousLineItems = await prepareOptimisticUpdate();

    // Optimistically update the UI immediately using new hierarchical query keys
    queryClient.setQueryData<LineItem[]>(
      QUERY_KEYS.calculations.lineItems(calculationId),
      (old) => (old ? old.filter((item) => item.id !== lineItemId) : [])
    );

    // Add to mutation queue with highest priority
    return mutationQueue.enqueue({
      type: "remove",
      priority: 12, // Highest priority for remove operations
      operation: async () => {
        const { error } = await supabase
          .from("calculation_line_items")
          .delete()
          .eq("id", lineItemId)
          .eq("calculation_id", calculationId);

        if (error) throw error;

        await handlePostMutation(true); // Use immediate recalculation for remove operations (more critical)
        return true;
      },
      onSuccess: () => {
        optimizedNotifications.lineItem.success("Item removed successfully");
        // Refresh data to ensure consistency using new hierarchical query keys
        queryClient.invalidateQueries({
          queryKey: QUERY_KEYS.calculations.lineItems(calculationId),
        });
      },
      onError: (error) => {
        console.error("Error removing line item:", error);
        showError("Failed to remove item. Please try again.");

        // Rollback optimistic update using new hierarchical query keys
        if (previousLineItems) {
          queryClient.setQueryData(
            QUERY_KEYS.calculations.lineItems(calculationId),
            previousLineItems
          );
        }
      },
    });
  };

  return {
    // Mutation functions
    addLineItem,
    updateLineItem,
    removeLineItem,

    // Queue state and controls
    queueState: mutationQueue.state,
    clearQueue: mutationQueue.clearQueue,
    getQueueStatus: mutationQueue.getQueueStatus,

    // Recalculation controls
    debouncedRecalculate,
    recalculateNow,

    // Computed values
    isProcessing: mutationQueue.state.isProcessing,
    hasOperations: mutationQueue.hasOperations,
    isIdle: mutationQueue.isIdle,
  };
}

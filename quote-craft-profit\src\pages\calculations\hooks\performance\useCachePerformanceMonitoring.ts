/**
 * Cache Performance Monitoring Hook
 * 
 * Phase 4 Performance Optimization: Monitors validation cache performance
 * and provides insights into cache efficiency and optimization opportunities.
 */

import { useEffect, useState, useCallback } from 'react';
import { validationCache, type CacheStatistics } from '@/lib/validationCache';

/**
 * Cache performance metrics interface
 */
export interface CachePerformanceMetrics {
  /** Current cache statistics */
  statistics: CacheStatistics;
  
  /** Cache efficiency rating (0-100) */
  efficiency: number;
  
  /** Performance recommendations */
  recommendations: string[];
  
  /** Cache health status */
  health: 'excellent' | 'good' | 'fair' | 'poor';
  
  /** Memory usage status */
  memoryStatus: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * Cache monitoring configuration
 */
export interface CacheMonitoringConfig {
  /** Whether to enable real-time monitoring */
  enabled?: boolean;
  
  /** Monitoring interval in milliseconds */
  interval?: number;
  
  /** Whether to log performance metrics */
  logMetrics?: boolean;
  
  /** Whether to show performance warnings */
  showWarnings?: boolean;
}

/**
 * Hook for monitoring validation cache performance
 * 
 * @param config - Cache monitoring configuration
 * @returns Cache performance metrics and management functions
 */
export const useCachePerformanceMonitoring = (
  config: CacheMonitoringConfig = {}
) => {
  const {
    enabled = process.env.NODE_ENV === 'development',
    interval = 5000, // 5 seconds
    logMetrics = true,
    showWarnings = true,
  } = config;

  const [metrics, setMetrics] = useState<CachePerformanceMetrics>(() => 
    calculateMetrics(validationCache.getStatistics())
  );

  // Update metrics periodically
  useEffect(() => {
    if (!enabled) return;

    const updateMetrics = () => {
      const stats = validationCache.getStatistics();
      const newMetrics = calculateMetrics(stats);
      setMetrics(newMetrics);

      // Log metrics if enabled
      if (logMetrics && stats.totalValidations > 0) {
        console.group('[Cache Performance] Validation Cache Metrics');
        console.log('Statistics:', stats);
        console.log('Efficiency:', newMetrics.efficiency + '%');
        console.log('Health:', newMetrics.health);
        console.log('Memory Status:', newMetrics.memoryStatus);
        if (newMetrics.recommendations.length > 0) {
          console.log('Recommendations:', newMetrics.recommendations);
        }
        console.groupEnd();
      }

      // Show warnings if enabled
      if (showWarnings && newMetrics.health === 'poor') {
        console.warn('[Cache Performance] Poor cache performance detected:', newMetrics.recommendations);
      }
    };

    // Initial update
    updateMetrics();

    // Set up interval
    const intervalId = setInterval(updateMetrics, interval);

    return () => clearInterval(intervalId);
  }, [enabled, interval, logMetrics, showWarnings]);

  // Manual metrics refresh
  const refreshMetrics = useCallback(() => {
    const stats = validationCache.getStatistics();
    const newMetrics = calculateMetrics(stats);
    setMetrics(newMetrics);
    return newMetrics;
  }, []);

  // Clear cache and reset metrics
  const clearCache = useCallback(() => {
    validationCache.clear();
    const stats = validationCache.getStatistics();
    const newMetrics = calculateMetrics(stats);
    setMetrics(newMetrics);
    
    if (logMetrics) {
      console.log('[Cache Performance] Cache cleared and metrics reset');
    }
  }, [logMetrics]);

  // Get detailed cache information
  const getCacheDetails = useCallback(() => {
    return {
      entries: validationCache.getEntries(),
      statistics: validationCache.getStatistics(),
      metrics: metrics,
    };
  }, [metrics]);

  // Preload common UUIDs
  const preloadUUIDs = useCallback((uuids: string[]) => {
    validationCache.preload(uuids);
    refreshMetrics();
    
    if (logMetrics) {
      console.log(`[Cache Performance] Preloaded ${uuids.length} UUIDs`);
    }
  }, [refreshMetrics, logMetrics]);

  return {
    metrics,
    refreshMetrics,
    clearCache,
    getCacheDetails,
    preloadUUIDs,
    isEnabled: enabled,
  };
};

/**
 * Calculate cache performance metrics from statistics
 * 
 * @param stats - Cache statistics
 * @returns Calculated performance metrics
 */
function calculateMetrics(stats: CacheStatistics): CachePerformanceMetrics {
  const efficiency = stats.hitRate;
  const health = calculateHealth(stats);
  const memoryStatus = calculateMemoryStatus(stats);
  const recommendations = generateRecommendations(stats);

  return {
    statistics: stats,
    efficiency,
    recommendations,
    health,
    memoryStatus,
  };
}

/**
 * Calculate cache health status
 * 
 * @param stats - Cache statistics
 * @returns Health status
 */
function calculateHealth(stats: CacheStatistics): 'excellent' | 'good' | 'fair' | 'poor' {
  if (stats.totalValidations === 0) return 'good'; // No data yet
  
  if (stats.hitRate >= 80) return 'excellent';
  if (stats.hitRate >= 60) return 'good';
  if (stats.hitRate >= 40) return 'fair';
  return 'poor';
}

/**
 * Calculate memory usage status
 * 
 * @param stats - Cache statistics
 * @returns Memory status
 */
function calculateMemoryStatus(stats: CacheStatistics): 'low' | 'medium' | 'high' | 'critical' {
  const usageRatio = stats.size / stats.maxSize;
  
  if (usageRatio < 0.5) return 'low';
  if (usageRatio < 0.75) return 'medium';
  if (usageRatio < 0.9) return 'high';
  return 'critical';
}

/**
 * Generate performance recommendations
 * 
 * @param stats - Cache statistics
 * @returns Array of recommendations
 */
function generateRecommendations(stats: CacheStatistics): string[] {
  const recommendations: string[] = [];

  // Hit rate recommendations
  if (stats.hitRate < 40 && stats.totalValidations > 10) {
    recommendations.push('Low cache hit rate - consider increasing cache size or TTL');
  }

  // Memory usage recommendations
  if (stats.size / stats.maxSize > 0.9) {
    recommendations.push('Cache is nearly full - consider increasing maxSize');
  }

  // Performance recommendations
  if (stats.averageValidationTime > 1) {
    recommendations.push('High validation time - cache is providing good performance benefit');
  }

  // Usage pattern recommendations
  if (stats.totalValidations > 100 && stats.hitRate < 20) {
    recommendations.push('Very low hit rate with high usage - check for UUID patterns or consider cache strategy');
  }

  // Size optimization
  if (stats.size < stats.maxSize * 0.1 && stats.totalValidations > 50) {
    recommendations.push('Cache size is underutilized - consider reducing maxSize to save memory');
  }

  return recommendations;
}

/**
 * Hook for cache statistics only (lightweight)
 * 
 * @returns Current cache statistics
 */
export const useCacheStatistics = () => {
  const [stats, setStats] = useState<CacheStatistics>(() => 
    validationCache.getStatistics()
  );

  const refresh = useCallback(() => {
    const newStats = validationCache.getStatistics();
    setStats(newStats);
    return newStats;
  }, []);

  return {
    statistics: stats,
    refresh,
  };
};

/**
 * Hook for cache management actions
 * 
 * @returns Cache management functions
 */
export const useCacheManagement = () => {
  const clear = useCallback(() => {
    validationCache.clear();
  }, []);

  const preload = useCallback((uuids: string[]) => {
    validationCache.preload(uuids);
  }, []);

  const getEntries = useCallback(() => {
    return validationCache.getEntries();
  }, []);

  return {
    clear,
    preload,
    getEntries,
  };
};

export default useCachePerformanceMonitoring;

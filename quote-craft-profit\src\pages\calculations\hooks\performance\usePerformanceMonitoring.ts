/**
 * Performance Monitoring Hook
 *
 * Phase 3 Performance Optimization: Tracks component performance metrics
 * and provides insights into rendering behavior and optimization opportunities.
 */

import { useEffect, useRef, useState, useCallback } from "react";

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  /** Component name being tracked */
  componentName: string;

  /** Number of renders since mount */
  renderCount: number;

  /** Time of first render */
  firstRenderTime: number;

  /** Time of last render */
  lastRenderTime: number;

  /** Average time between renders */
  averageRenderInterval: number;

  /** Total time spent rendering */
  totalRenderTime: number;

  /** Validation performance metrics */
  validationMetrics?: {
    validationCount: number;
    totalValidationTime: number;
    averageValidationTime: number;
  };

  /** Hook performance metrics */
  hookMetrics?: {
    hookCallCount: number;
    totalHookTime: number;
    averageHookTime: number;
  };
}

/**
 * Performance tracking configuration
 */
export interface PerformanceConfig {
  /** Whether to enable performance tracking */
  enabled?: boolean;

  /** Whether to log metrics to console */
  logToConsole?: boolean;

  /** Interval for logging metrics (in renders) */
  logInterval?: number;

  /** Whether to track validation performance */
  trackValidation?: boolean;

  /** Whether to track hook performance */
  trackHooks?: boolean;
}

/**
 * Hook for tracking component performance
 *
 * @param componentName - Name of the component being tracked
 * @param config - Performance tracking configuration
 * @returns Performance metrics and tracking functions
 */
export const usePerformanceMonitoring = (
  componentName: string,
  config: PerformanceConfig = {}
) => {
  const {
    enabled = process.env.NODE_ENV === "development",
    logToConsole = true,
    logInterval = 10,
    trackValidation = true,
    trackHooks = true,
  } = config;

  // Performance tracking state
  const [metrics, setMetrics] = useState<PerformanceMetrics>(() => ({
    componentName,
    renderCount: 0,
    firstRenderTime: performance.now(),
    lastRenderTime: performance.now(),
    averageRenderInterval: 0,
    totalRenderTime: 0,
  }));

  // Refs for tracking
  const renderTimesRef = useRef<number[]>([]);
  const validationTimesRef = useRef<number[]>([]);
  const hookTimesRef = useRef<number[]>([]);
  const lastRenderTimeRef = useRef<number>(performance.now());

  // Track render performance - FIXED: Added proper dependency array
  useEffect(() => {
    if (!enabled) return;

    const renderTime = performance.now();
    const timeSinceLastRender = renderTime - lastRenderTimeRef.current;

    renderTimesRef.current.push(timeSinceLastRender);
    lastRenderTimeRef.current = renderTime;

    setMetrics((prev) => {
      const newRenderCount = prev.renderCount + 1;
      const totalRenderTime = renderTimesRef.current.reduce(
        (sum, time) => sum + time,
        0
      );
      const averageRenderInterval = totalRenderTime / newRenderCount;

      const newMetrics: PerformanceMetrics = {
        ...prev,
        renderCount: newRenderCount,
        lastRenderTime: renderTime,
        averageRenderInterval,
        totalRenderTime,
      };

      // Add validation metrics if tracking is enabled
      if (trackValidation && validationTimesRef.current.length > 0) {
        const totalValidationTime = validationTimesRef.current.reduce(
          (sum, time) => sum + time,
          0
        );
        newMetrics.validationMetrics = {
          validationCount: validationTimesRef.current.length,
          totalValidationTime,
          averageValidationTime:
            totalValidationTime / validationTimesRef.current.length,
        };
      }

      // Add hook metrics if tracking is enabled
      if (trackHooks && hookTimesRef.current.length > 0) {
        const totalHookTime = hookTimesRef.current.reduce(
          (sum, time) => sum + time,
          0
        );
        newMetrics.hookMetrics = {
          hookCallCount: hookTimesRef.current.length,
          totalHookTime,
          averageHookTime: totalHookTime / hookTimesRef.current.length,
        };
      }

      // Log metrics at intervals
      if (logToConsole && newRenderCount % logInterval === 0) {
        console.group(
          `[Performance] ${componentName} - Render #${newRenderCount}`
        );
        console.log("Metrics:", newMetrics);
        console.log("Recent render times:", renderTimesRef.current.slice(-5));
        if (newMetrics.validationMetrics) {
          console.log("Validation metrics:", newMetrics.validationMetrics);
        }
        if (newMetrics.hookMetrics) {
          console.log("Hook metrics:", newMetrics.hookMetrics);
        }
        console.groupEnd();
      }

      return newMetrics;
    });
  }, [
    enabled,
    trackValidation,
    trackHooks,
    logToConsole,
    logInterval,
    componentName,
  ]);

  // Function to track validation performance
  const trackValidationPerformance = useCallback(
    (validationFn: () => any) => {
      if (!enabled || !trackValidation) {
        return validationFn();
      }

      const startTime = performance.now();
      const result = validationFn();
      const endTime = performance.now();

      validationTimesRef.current.push(endTime - startTime);

      return result;
    },
    [enabled, trackValidation]
  );

  // Function to track hook performance
  const trackHookPerformance = useCallback(
    <T>(hookName: string, hookFn: () => T): T => {
      if (!enabled || !trackHooks) {
        return hookFn();
      }

      const startTime = performance.now();
      const result = hookFn();
      const endTime = performance.now();

      hookTimesRef.current.push(endTime - startTime);

      if (logToConsole && hookTimesRef.current.length % 5 === 0) {
        console.log(
          `[Performance] ${componentName} - ${hookName}: ${(
            endTime - startTime
          ).toFixed(2)}ms`
        );
      }

      return result;
    },
    [enabled, trackHooks, logToConsole, componentName]
  );

  // Function to get performance summary
  const getPerformanceSummary = useCallback(() => {
    return {
      ...metrics,
      isPerformant: metrics.averageRenderInterval < 16.67, // 60fps threshold
      renderFrequency:
        metrics.renderCount /
        ((metrics.lastRenderTime - metrics.firstRenderTime) / 1000),
      recommendations: generateRecommendations(metrics),
    };
  }, [metrics]);

  // Function to reset metrics
  const resetMetrics = useCallback(() => {
    renderTimesRef.current = [];
    validationTimesRef.current = [];
    hookTimesRef.current = [];
    lastRenderTimeRef.current = performance.now();

    setMetrics({
      componentName,
      renderCount: 0,
      firstRenderTime: performance.now(),
      lastRenderTime: performance.now(),
      averageRenderInterval: 0,
      totalRenderTime: 0,
    });
  }, [componentName]);

  return {
    metrics,
    trackValidationPerformance,
    trackHookPerformance,
    getPerformanceSummary,
    resetMetrics,
    isEnabled: enabled,
  };
};

/**
 * Generate performance recommendations based on metrics
 */
function generateRecommendations(metrics: PerformanceMetrics): string[] {
  const recommendations: string[] = [];

  // Check render frequency
  if (metrics.averageRenderInterval < 16.67) {
    recommendations.push(
      "Consider memoizing components or props to reduce render frequency"
    );
  }

  // Check validation performance
  if (
    metrics.validationMetrics &&
    metrics.validationMetrics.averageValidationTime > 1
  ) {
    recommendations.push(
      "Validation is taking longer than 1ms - consider caching validation results"
    );
  }

  // Check hook performance
  if (metrics.hookMetrics && metrics.hookMetrics.averageHookTime > 5) {
    recommendations.push(
      "Hook execution is taking longer than 5ms - consider optimizing hook dependencies"
    );
  }

  // Check total render count
  if (metrics.renderCount > 50) {
    recommendations.push(
      "High render count detected - investigate unnecessary re-renders"
    );
  }

  return recommendations;
}

/**
 * Hook for tracking validation performance specifically
 */
export const useValidationPerformance = (componentName: string) => {
  const validationTimesRef = useRef<number[]>([]);

  const trackValidation = useCallback(
    (id: string | undefined, validationFn: () => boolean) => {
      const startTime = performance.now();
      const result = validationFn();
      const endTime = performance.now();

      validationTimesRef.current.push(endTime - startTime);

      if (
        process.env.NODE_ENV === "development" &&
        validationTimesRef.current.length % 10 === 0
      ) {
        const avgTime =
          validationTimesRef.current.reduce((sum, time) => sum + time, 0) /
          validationTimesRef.current.length;
        console.log(
          `[Validation Performance] ${componentName}: Average ${avgTime.toFixed(
            2
          )}ms over ${validationTimesRef.current.length} validations`
        );
      }

      return result;
    },
    [componentName]
  );

  return { trackValidation };
};

export default usePerformanceMonitoring;

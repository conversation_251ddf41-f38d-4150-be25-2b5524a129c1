/**
 * Split Calculation Detail Content Components
 *
 * Phase 3 Performance Optimization: Split heavy components into smaller,
 * more focused components to reduce re-render impact and improve performance.
 */

import React, { memo } from "react";
import CalculationDetailHeader from "./detail/layout/CalculationDetailHeader";
import CalculationDetailContent from "./detail/layout/CalculationDetailContent";
import { CalculationProvider } from "../contexts";
import type { CalculationDetails } from "@/types";

// Simple placeholder for migration status indicator
const MigrationStatusIndicator = ({ visible }: { visible?: boolean }) => {
  if (!visible) return null;
  return <div className="text-xs text-gray-500 p-2">Migration Status: OK</div>;
};

/**
 * Props for calculation header section
 */
interface CalculationHeaderSectionProps {
  calculation: CalculationDetails;
  onNavigateBack: () => void;
}

/**
 * Memoized header section component
 * Only re-renders when calculation name/status changes
 */
export const CalculationHeaderSection = memo<CalculationHeaderSectionProps>(
  ({ calculation, onNavigateBack }) => {
    return (
      <CalculationDetailHeader
        name={calculation.name}
        status={calculation.status}
        onNavigateBack={onNavigateBack}
      />
    );
  }
);

CalculationHeaderSection.displayName = "CalculationHeaderSection";

/**
 * Props for calculation content section
 */
interface CalculationContentSectionProps {
  calculationId: string;
  state: any;
  actions: any;
}

/**
 * Memoized content section component
 * Only re-renders when state or actions change
 */
export const CalculationContentSection = memo<CalculationContentSectionProps>(
  ({ calculationId, state, actions }) => {
    return (
      <CalculationProvider
        calculationId={calculationId}
        state={state}
        actions={actions}
      >
        <CalculationDetailContent />
      </CalculationProvider>
    );
  }
);

CalculationContentSection.displayName = "CalculationContentSection";

/**
 * Props for error boundary section
 */
interface ErrorBoundarySectionProps {
  calculationId: string;
  onRetry: () => void;
  onNavigateBack: () => void;
  children: React.ReactNode;
}

/**
 * Memoized error boundary section
 * Only re-renders when handlers change
 */
export const ErrorBoundarySection = memo<ErrorBoundarySectionProps>(
  ({ children }) => {
    // Simple error boundary wrapper - just render children
    // TODO: Implement proper error boundary if needed
    return <div>{children}</div>;
  }
);

ErrorBoundarySection.displayName = "ErrorBoundarySection";

/**
 * Props for migration indicator section
 */
interface MigrationIndicatorSectionProps {
  migrationStatus: any;
  visible: boolean;
}

/**
 * Memoized migration indicator section
 * Only re-renders when migration status changes
 */
export const MigrationIndicatorSection = memo<MigrationIndicatorSectionProps>(
  ({ migrationStatus, visible }) => {
    if (!visible) return null;

    return <MigrationStatusIndicator {...migrationStatus} />;
  }
);

MigrationIndicatorSection.displayName = "MigrationIndicatorSection";

/**
 * Props for loading section
 */
interface LoadingSectionProps {
  isLoading: boolean;
  calculationName?: string;
}

/**
 * Memoized loading section component
 * Only re-renders when loading state changes
 */
export const LoadingSection = memo<LoadingSectionProps>(
  ({ isLoading, calculationName }) => {
    if (!isLoading) return null;

    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {calculationName
              ? `Loading ${calculationName}...`
              : "Loading calculation..."}
          </p>
        </div>
      </div>
    );
  }
);

LoadingSection.displayName = "LoadingSection";

/**
 * Props for error section
 */
interface ErrorSectionProps {
  isError: boolean;
  hasAttemptedFetch: boolean;
  errorMessage?: string;
  onRetry?: () => void;
}

/**
 * Memoized error section component
 * Only re-renders when error state changes
 */
export const ErrorSection = memo<ErrorSectionProps>(
  ({ isError, hasAttemptedFetch, errorMessage, onRetry }) => {
    if (!isError || !hasAttemptedFetch) return null;

    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg
              className="w-12 h-12 mx-auto"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {errorMessage || "Failed to load calculation"}
          </h3>
          <p className="text-gray-600 mb-4">
            There was an error loading the calculation details.
          </p>
          {onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Try Again
            </button>
          )}
        </div>
      </div>
    );
  }
);

ErrorSection.displayName = "ErrorSection";

/**
 * Performance optimized calculation detail layout
 * Uses memoized sections to minimize re-render impact
 */
export interface OptimizedCalculationDetailLayoutProps {
  calculation: CalculationDetails;
  calculationId: string;
  state: any;
  actions: any;
  isLoading: boolean;
  isError: boolean;
  hasAttemptedFetch: boolean;
  migrationStatus: any;
  onRetry: () => void;
}

export const OptimizedCalculationDetailLayout =
  memo<OptimizedCalculationDetailLayoutProps>(
    ({
      calculation,
      calculationId,
      state,
      actions,
      isLoading,
      isError,
      hasAttemptedFetch,
      migrationStatus,
      onRetry,
    }) => {
      // Show loading state
      if (isLoading) {
        return (
          <LoadingSection
            isLoading={isLoading}
            calculationName={calculation?.name}
          />
        );
      }

      // Show error state
      if (isError && hasAttemptedFetch) {
        return (
          <ErrorSection
            isError={isError}
            hasAttemptedFetch={hasAttemptedFetch}
            errorMessage="Calculation not found"
            onRetry={onRetry}
          />
        );
      }

      // Show main content
      return (
        <ErrorBoundarySection
          calculationId={calculationId}
          onRetry={onRetry}
          onNavigateBack={actions.handleNavigateBack}
        >
          <CalculationHeaderSection
            calculation={calculation}
            onNavigateBack={actions.handleNavigateBack}
          />

          <CalculationContentSection
            calculationId={calculationId}
            state={state}
            actions={actions}
          />

          <MigrationIndicatorSection
            migrationStatus={migrationStatus}
            visible={process.env.NODE_ENV === "development"}
          />
        </ErrorBoundarySection>
      );
    }
  );

OptimizedCalculationDetailLayout.displayName =
  "OptimizedCalculationDetailLayout";
